<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التقارير الشامل - v4</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }
        .test-section:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.2);
        }
        .result {
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }
        .result.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            color: #155724;
        }
        .result.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border: 2px solid #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            color: #856404;
        }
        .result.info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border: 2px solid #17a2b8;
            color: #0c5460;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        button:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        .status-indicator {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-left: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .status-success { background: linear-gradient(135deg, #28a745, #20c997); }
        .status-error { background: linear-gradient(135deg, #dc3545, #e74c3c); }
        .status-warning { background: linear-gradient(135deg, #ffc107, #f39c12); }
        .report-preview {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .validation-results {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .image-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ اختبار نظام التقارير الشامل - v4</h1>
            <p>اختبار شامل لجميع التحديثات: formatSinglePageReport + validateReportCompleteness + الصور الفعلية</p>
        </div>

        <div class="test-section">
            <h3>🔧 1. تهيئة النظام مع البيانات الشاملة</h3>
            <button onclick="initializeSystemWithData()">تهيئة النظام</button>
            <div id="init-result" class="result info">انقر على الزر لبدء التهيئة...</div>
        </div>

        <div class="test-section">
            <h3>📊 2. اختبار validateReportCompleteness</h3>
            <button onclick="testReportValidation()">اختبار التحقق من التقرير</button>
            <div id="validation-result" class="result info">انقر على الزر لبدء الاختبار...</div>
            <div id="validation-details" class="validation-results" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 3. اختبار formatSinglePageReport المحدث</h3>
            <button onclick="testFormatSinglePageReport()">اختبار تنسيق التقرير</button>
            <div id="format-result" class="result info">انقر على الزر لبدء الاختبار...</div>
            <div id="report-preview" class="report-preview" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📸 4. اختبار ensureVulnerabilityImages</h3>
            <button onclick="testEnsureImages()">اختبار ضمان الصور</button>
            <div id="images-result" class="result info">انقر على الزر لبدء الاختبار...</div>
            <div id="images-grid" class="image-grid"></div>
        </div>

        <div class="test-section">
            <h3>🎯 5. اختبار التكامل الكامل</h3>
            <button onclick="testFullIntegration()">اختبار التكامل الكامل</button>
            <div id="integration-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📄 6. إنشاء تقرير نهائي شامل</h3>
            <button onclick="generateComprehensiveReport()">إنشاء التقرير النهائي</button>
            <div id="final-result" class="result info">انقر على الزر لإنشاء التقرير...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testVulnerabilities = [];

        // تهيئة النظام مع البيانات الشاملة
        async function initializeSystemWithData() {
            const resultDiv = document.getElementById('init-result');
            resultDiv.textContent = 'جاري التهيئة مع البيانات الشاملة...';
            resultDiv.className = 'result info';
            
            try {
                console.log('🔧 تهيئة نظام Bug Bounty v4 مع البيانات الشاملة...');
                
                // تهيئة BugBountyCore
                bugBountyCore = new BugBountyCore();
                
                // تهيئة analysisState
                bugBountyCore.analysisState = {
                    reportId: `comprehensive_test_${Date.now()}`,
                    vulnerabilities: [],
                    websiteData: { url: 'https://example.com' },
                    vulnerabilityScreenshots: {}
                };
                
                // إنشاء ثغرات اختبار شاملة مع جميع البيانات المطلوبة
                testVulnerabilities = [
                    {
                        name: 'Advanced_Cross_Site_Scripting',
                        type: 'XSS',
                        category: 'Injection',
                        severity: 'High',
                        cvss_score: 7.5,
                        description: 'ثغرة XSS متقدمة تسمح بتنفيذ كود JavaScript خبيث في متصفح المستخدم',
                        location: 'https://example.com/search',
                        impact: 'يمكن للمهاجم سرقة cookies المستخدم وتنفيذ عمليات غير مصرح بها',
                        remediation: 'تطبيق input validation وoutput encoding',
                        cwe: 'CWE-79',
                        owasp: 'A03:2021 – Injection',
                        exploitation_result: 'تم تنفيذ payload بنجاح وظهور alert box',
                        exploitation_steps: '1. إدخال payload في حقل البحث\n2. إرسال النموذج\n3. تنفيذ الكود JavaScript',
                        impact_changes: 'ظهور alert box، تغيير في DOM، تنفيذ كود خبيث',
                        technical_details: {
                            payload: "<script>alert('XSS')</script>",
                            response_code: 200,
                            vulnerable_parameter: 'search'
                        },
                        dialogue: 'تم اكتشاف ثغرة XSS خطيرة تتطلب إصلاح فوري',
                        visual_proof: {
                            before_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            during_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            after_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                        },
                        images: {
                            before: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            during: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            after: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                        }
                    },
                    {
                        name: 'Critical_SQL_Injection_Attack',
                        type: 'SQL Injection',
                        category: 'Injection',
                        severity: 'Critical',
                        cvss_score: 9.8,
                        description: 'ثغرة SQL Injection حرجة تسمح بالوصول غير المصرح به لقاعدة البيانات',
                        location: 'https://example.com/login',
                        impact: 'يمكن للمهاجم قراءة وتعديل وحذف بيانات قاعدة البيانات',
                        remediation: 'استخدام prepared statements وparameter binding',
                        cwe: 'CWE-89',
                        owasp: 'A03:2021 – Injection',
                        exploitation_result: 'تم الوصول لقاعدة البيانات وقراءة جدول المستخدمين',
                        exploitation_steps: '1. إدخال SQL payload في حقل username\n2. تجاوز المصادقة\n3. الوصول لبيانات حساسة',
                        impact_changes: 'تجاوز نظام المصادقة، الوصول لبيانات المستخدمين، تعديل قاعدة البيانات',
                        technical_details: {
                            payload: "admin' OR '1'='1' --",
                            response_code: 200,
                            vulnerable_parameter: 'username',
                            database_error: 'MySQL syntax error detected'
                        },
                        dialogue: 'ثغرة SQL Injection حرجة تتطلب إصلاح عاجل لحماية قاعدة البيانات',
                        visual_proof: {
                            before_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            during_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                            after_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                        }
                    }
                ];
                
                // إضافة الثغرات إلى النظام
                bugBountyCore.analysisState.vulnerabilities = testVulnerabilities;
                
                // تهيئة المكونات الأخرى
                await bugBountyCore.initializeTextualImpactAnalyzer();
                await bugBountyCore.initializeImpactVisualizer();
                
                const hasCore = bugBountyCore !== null;
                const hasVulnerabilities = testVulnerabilities.length > 0;
                const hasCompleteData = testVulnerabilities.every(v => 
                    v.exploitation_steps && v.impact_changes && v.technical_details && v.dialogue
                );
                const hasImages = testVulnerabilities.every(v => 
                    v.visual_proof || v.images
                );
                
                resultDiv.innerHTML = `✅ نتيجة التهيئة مع البيانات الشاملة:

📊 المكونات الأساسية:
<span class="status-indicator ${hasCore ? 'status-success' : 'status-error'}"></span> BugBountyCore: ${hasCore ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasVulnerabilities ? 'status-success' : 'status-error'}"></span> ثغرات الاختبار: ${testVulnerabilities.length}
<span class="status-indicator ${hasCompleteData ? 'status-success' : 'status-error'}"></span> بيانات شاملة: ${hasCompleteData ? 'متاحة' : 'ناقصة'}
<span class="status-indicator ${hasImages ? 'status-success' : 'status-error'}"></span> صور الثغرات: ${hasImages ? 'متاحة' : 'ناقصة'}

📋 تفاصيل البيانات:
- معرف التقرير: ${bugBountyCore.analysisState.reportId}
- عدد الثغرات: ${testVulnerabilities.length}
- ثغرات مع exploitation_steps: ${testVulnerabilities.filter(v => v.exploitation_steps).length}
- ثغرات مع impact_changes: ${testVulnerabilities.filter(v => v.impact_changes).length}
- ثغرات مع technical_details: ${testVulnerabilities.filter(v => v.technical_details).length}
- ثغرات مع dialogue: ${testVulnerabilities.filter(v => v.dialogue).length}
- ثغرات مع صور: ${testVulnerabilities.filter(v => v.visual_proof || v.images).length}

${hasCore && hasVulnerabilities && hasCompleteData && hasImages ? 
  '🎉 النظام جاهز للاختبار الشامل مع جميع البيانات المطلوبة!' : 
  '⚠️ بعض البيانات ناقصة - تحقق من المتطلبات'}`;
                
                resultDiv.className = hasCore && hasVulnerabilities && hasCompleteData && hasImages ? 'result success' : 'result warning';
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في التهيئة: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار validateReportCompleteness
        async function testReportValidation() {
            const resultDiv = document.getElementById('validation-result');
            const detailsDiv = document.getElementById('validation-details');

            resultDiv.textContent = 'جاري اختبار التحقق من التقرير...';
            resultDiv.className = 'result info';
            detailsDiv.style.display = 'none';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('🔍 اختبار validateReportCompleteness...');

                // اختبار مع الثغرات الكاملة
                const validationResults = bugBountyCore.validateReportCompleteness(testVulnerabilities);

                // اختبار مع ثغرات ناقصة
                const incompleteVulns = [
                    {
                        name: 'Incomplete_Vulnerability',
                        type: 'XSS',
                        severity: 'Medium'
                        // بيانات ناقصة عمداً
                    }
                ];

                const incompleteValidation = bugBountyCore.validateReportCompleteness(incompleteVulns);

                resultDiv.innerHTML = `✅ نتيجة اختبار validateReportCompleteness:

📊 اختبار الثغرات الكاملة:
<span class="status-indicator ${validationResults.isComplete ? 'status-success' : 'status-error'}"></span> التقرير مكتمل: ${validationResults.isComplete ? 'نعم' : 'لا'}
- إجمالي الثغرات: ${validationResults.totalVulnerabilities}
- ثغرات مع صور: ${validationResults.vulnerabilitiesWithImages}
- ثغرات مع خطوات استغلال: ${validationResults.vulnerabilitiesWithExploitationSteps}
- ثغرات مع تغيرات التأثير: ${validationResults.vulnerabilitiesWithImpactChanges}
- ثغرات مع حوار: ${validationResults.vulnerabilitiesWithDialogue}
- ثغرات مع تفاصيل تقنية: ${validationResults.vulnerabilitiesWithTechnicalDetails}

📊 اختبار الثغرات الناقصة:
<span class="status-indicator ${incompleteValidation.isComplete ? 'status-error' : 'status-success'}"></span> التقرير ناقص (متوقع): ${!incompleteValidation.isComplete ? 'نعم' : 'لا'}
- العناصر المفقودة: ${incompleteValidation.missingElements.length}
- ثغرات مع مشاكل: ${incompleteValidation.vulnerabilitiesWithIssues.length}

${validationResults.isComplete && !incompleteValidation.isComplete ?
  '🎉 دالة validateReportCompleteness تعمل بشكل صحيح!' :
  '⚠️ دالة validateReportCompleteness تحتاج مراجعة'}`;

                // عرض التفاصيل
                detailsDiv.innerHTML = `
                <h4>📋 تفاصيل التحقق:</h4>
                <p><strong>العناصر المفقودة في الاختبار الناقص:</strong></p>
                <ul>
                    ${incompleteValidation.missingElements.map(item => `<li>${item}</li>`).join('')}
                </ul>
                <p><strong>الثغرات مع مشاكل:</strong></p>
                <ul>
                    ${incompleteValidation.vulnerabilitiesWithIssues.map(vuln =>
                        `<li>${vuln.name}: ${vuln.issues.join(', ')}</li>`
                    ).join('')}
                </ul>
                `;
                detailsDiv.style.display = 'block';

                resultDiv.className = validationResults.isComplete && !incompleteValidation.isComplete ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التحقق: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار formatSinglePageReport المحدث
        async function testFormatSinglePageReport() {
            const resultDiv = document.getElementById('format-result');
            const previewDiv = document.getElementById('report-preview');

            resultDiv.textContent = 'جاري اختبار تنسيق التقرير...';
            resultDiv.className = 'result info';
            previewDiv.style.display = 'none';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('📋 اختبار formatSinglePageReport...');

                // إنشاء بيانات صفحة للاختبار
                const pageData = {
                    page_name: 'صفحة الاختبار الشاملة',
                    page_url: 'https://example.com/test',
                    vulnerabilities: testVulnerabilities,
                    interactive_dialogues: ['حوار تفاعلي 1', 'حوار تفاعلي 2'],
                    screenshots: ['screenshot1.png', 'screenshot2.png'],
                    scan_timestamp: new Date().toISOString(),
                    force_comprehensive: true
                };

                // تشغيل formatSinglePageReport
                const reportHTML = await bugBountyCore.formatSinglePageReport(pageData);

                // تحليل محتوى التقرير
                const hasTitle = reportHTML.includes('تقرير Bug Bounty الشامل');
                const hasVulnerabilities = reportHTML.includes('الثغرات المكتشفة والمستغلة');
                const hasImages = reportHTML.includes('data:image/png;base64,');
                const hasExploitationSteps = reportHTML.includes('خطوات الاستغلال');
                const hasImpactChanges = reportHTML.includes('التغيرات في النظام');
                const hasTechnicalDetails = reportHTML.includes('التفاصيل التقنية');
                const hasDialogue = reportHTML.includes('الحوار التفاعلي');
                const hasRemediation = reportHTML.includes('التوصيات للإصلاح');
                const hasVisualEvidence = reportHTML.includes('الأدلة البصرية الحقيقية');

                const reportLength = reportHTML.length;
                const imageCount = (reportHTML.match(/data:image\/png;base64,/g) || []).length;

                resultDiv.innerHTML = `✅ نتيجة اختبار formatSinglePageReport:

📊 محتوى التقرير:
<span class="status-indicator ${hasTitle ? 'status-success' : 'status-error'}"></span> عنوان شامل: ${hasTitle ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasVulnerabilities ? 'status-success' : 'status-error'}"></span> قسم الثغرات: ${hasVulnerabilities ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasImages ? 'status-success' : 'status-error'}"></span> الصور المدمجة: ${hasImages ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasExploitationSteps ? 'status-success' : 'status-error'}"></span> خطوات الاستغلال: ${hasExploitationSteps ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasImpactChanges ? 'status-success' : 'status-error'}"></span> تغيرات التأثير: ${hasImpactChanges ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasTechnicalDetails ? 'status-success' : 'status-error'}"></span> التفاصيل التقنية: ${hasTechnicalDetails ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasDialogue ? 'status-success' : 'status-error'}"></span> الحوار التفاعلي: ${hasDialogue ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasRemediation ? 'status-success' : 'status-error'}"></span> التوصيات: ${hasRemediation ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasVisualEvidence ? 'status-success' : 'status-error'}"></span> الأدلة البصرية: ${hasVisualEvidence ? 'موجودة' : 'مفقودة'}

📋 إحصائيات التقرير:
- طول التقرير: ${reportLength} حرف
- عدد الصور المدمجة: ${imageCount}
- عدد الثغرات: ${testVulnerabilities.length}

${hasTitle && hasVulnerabilities && hasImages && hasExploitationSteps && hasImpactChanges ?
  '🎉 formatSinglePageReport يعمل بشكل شامل ومكتمل!' :
  '⚠️ formatSinglePageReport يحتاج تحسين'}`;

                // عرض معاينة التقرير
                previewDiv.innerHTML = reportHTML.substring(0, 2000) + '...';
                previewDiv.style.display = 'block';

                resultDiv.className = hasTitle && hasVulnerabilities && hasImages && hasExploitationSteps && hasImpactChanges ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار تنسيق التقرير: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار ensureVulnerabilityImages
        async function testEnsureImages() {
            const resultDiv = document.getElementById('images-result');
            const imagesGrid = document.getElementById('images-grid');

            resultDiv.textContent = 'جاري اختبار ضمان الصور...';
            resultDiv.className = 'result info';
            imagesGrid.innerHTML = '';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('📸 اختبار ensureVulnerabilityImages...');

                // إنشاء ثغرة بدون صور للاختبار
                const vulnWithoutImages = {
                    name: 'Test_Vulnerability_Without_Images',
                    type: 'XSS',
                    severity: 'Medium',
                    location: 'https://example.com/test'
                };

                const testVulns = [...testVulnerabilities, vulnWithoutImages];

                // عد الصور قبل التشغيل
                const imagesBefore = bugBountyCore.countTotalScreenshots(testVulns);

                // تشغيل ensureVulnerabilityImages
                await bugBountyCore.ensureVulnerabilityImages(testVulns, 'https://example.com');

                // عد الصور بعد التشغيل
                const imagesAfter = bugBountyCore.countTotalScreenshots(testVulns);

                // التحقق من وجود الصور
                const vulnsWithImages = testVulns.filter(v => bugBountyCore.hasVulnerabilityImages(v)).length;
                const vulnsWithoutImages = testVulns.length - vulnsWithImages;

                resultDiv.innerHTML = `✅ نتيجة اختبار ensureVulnerabilityImages:

📊 إحصائيات الصور:
- الصور قبل التشغيل: ${imagesBefore}
- الصور بعد التشغيل: ${imagesAfter}
- الثغرات مع صور: ${vulnsWithImages}/${testVulns.length}
- الثغرات بدون صور: ${vulnsWithoutImages}

📸 تفاصيل الثغرات:
${testVulns.map((v, i) => `
- ${v.name}: ${bugBountyCore.hasVulnerabilityImages(v) ? '✅ لديها صور' : '❌ بدون صور'}`).join('')}

${vulnsWithImages === testVulns.length ?
  '🎉 ensureVulnerabilityImages يعمل بنجاح - جميع الثغرات لديها صور!' :
  '⚠️ ensureVulnerabilityImages يحتاج تحسين - بعض الثغرات بدون صور'}`;

                // عرض الصور في الشبكة
                testVulns.forEach(vuln => {
                    if (bugBountyCore.hasVulnerabilityImages(vuln)) {
                        const stages = ['before', 'during', 'after'];
                        stages.forEach(stage => {
                            const image = bugBountyCore.getVulnerabilityImage(vuln, stage);
                            if (image) {
                                const imageItem = document.createElement('div');
                                imageItem.className = 'image-item';
                                imageItem.innerHTML = `
                                    <h4>${vuln.name} - ${stage}</h4>
                                    <img src="data:image/png;base64,${image}" alt="${stage}">
                                    <p>✅ صورة متاحة</p>
                                `;
                                imagesGrid.appendChild(imageItem);
                            }
                        });
                    }
                });

                resultDiv.className = vulnsWithImages === testVulns.length ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار ضمان الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار validateReportCompleteness
        async function testReportValidation() {
            const resultDiv = document.getElementById('validation-result');
            const detailsDiv = document.getElementById('validation-details');

            resultDiv.textContent = 'جاري اختبار التحقق من التقرير...';
            resultDiv.className = 'result info';
            detailsDiv.style.display = 'none';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('🔍 اختبار validateReportCompleteness...');

                // اختبار مع الثغرات الكاملة
                const validationResults = bugBountyCore.validateReportCompleteness(testVulnerabilities);

                // اختبار مع ثغرات ناقصة
                const incompleteVulns = [
                    {
                        name: 'Incomplete_Vulnerability',
                        type: 'XSS',
                        severity: 'Medium'
                        // بيانات ناقصة عمداً
                    }
                ];

                const incompleteValidation = bugBountyCore.validateReportCompleteness(incompleteVulns);

                resultDiv.innerHTML = `✅ نتيجة اختبار validateReportCompleteness:

📊 اختبار الثغرات الكاملة:
<span class="status-indicator ${validationResults.isComplete ? 'status-success' : 'status-error'}"></span> التقرير مكتمل: ${validationResults.isComplete ? 'نعم' : 'لا'}
- إجمالي الثغرات: ${validationResults.totalVulnerabilities}
- ثغرات مع صور: ${validationResults.vulnerabilitiesWithImages}
- ثغرات مع خطوات استغلال: ${validationResults.vulnerabilitiesWithExploitationSteps}
- ثغرات مع تغيرات التأثير: ${validationResults.vulnerabilitiesWithImpactChanges}
- ثغرات مع حوار: ${validationResults.vulnerabilitiesWithDialogue}
- ثغرات مع تفاصيل تقنية: ${validationResults.vulnerabilitiesWithTechnicalDetails}

📊 اختبار الثغرات الناقصة:
<span class="status-indicator ${incompleteValidation.isComplete ? 'status-error' : 'status-success'}"></span> التقرير ناقص (متوقع): ${!incompleteValidation.isComplete ? 'نعم' : 'لا'}
- العناصر المفقودة: ${incompleteValidation.missingElements.length}
- ثغرات مع مشاكل: ${incompleteValidation.vulnerabilitiesWithIssues.length}

${validationResults.isComplete && !incompleteValidation.isComplete ?
  '🎉 دالة validateReportCompleteness تعمل بشكل صحيح!' :
  '⚠️ دالة validateReportCompleteness تحتاج مراجعة'}`;

                // عرض التفاصيل
                detailsDiv.innerHTML = `
                <h4>📋 تفاصيل التحقق:</h4>
                <p><strong>العناصر المفقودة في الاختبار الناقص:</strong></p>
                <ul>
                    ${incompleteValidation.missingElements.map(item => `<li>${item}</li>`).join('')}
                </ul>
                <p><strong>الثغرات مع مشاكل:</strong></p>
                <ul>
                    ${incompleteValidation.vulnerabilitiesWithIssues.map(vuln =>
                        `<li>${vuln.name}: ${vuln.issues.join(', ')}</li>`
                    ).join('')}
                </ul>
                `;
                detailsDiv.style.display = 'block';

                resultDiv.className = validationResults.isComplete && !incompleteValidation.isComplete ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التحقق: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار formatSinglePageReport المحدث
        async function testFormatSinglePageReport() {
            const resultDiv = document.getElementById('format-result');
            const previewDiv = document.getElementById('report-preview');

            resultDiv.textContent = 'جاري اختبار تنسيق التقرير...';
            resultDiv.className = 'result info';
            previewDiv.style.display = 'none';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('📋 اختبار formatSinglePageReport...');

                // إنشاء بيانات صفحة للاختبار
                const pageData = {
                    page_name: 'صفحة الاختبار الشاملة',
                    page_url: 'https://example.com/test',
                    vulnerabilities: testVulnerabilities,
                    interactive_dialogues: ['حوار تفاعلي 1', 'حوار تفاعلي 2'],
                    screenshots: ['screenshot1.png', 'screenshot2.png'],
                    scan_timestamp: new Date().toISOString(),
                    force_comprehensive: true
                };

                // تشغيل formatSinglePageReport
                const reportHTML = await bugBountyCore.formatSinglePageReport(pageData);

                // تحليل محتوى التقرير
                const hasTitle = reportHTML.includes('تقرير Bug Bounty الشامل');
                const hasVulnerabilities = reportHTML.includes('الثغرات المكتشفة والمستغلة');
                const hasImages = reportHTML.includes('data:image/png;base64,');
                const hasExploitationSteps = reportHTML.includes('خطوات الاستغلال');
                const hasImpactChanges = reportHTML.includes('التغيرات في النظام');
                const hasTechnicalDetails = reportHTML.includes('التفاصيل التقنية');
                const hasDialogue = reportHTML.includes('الحوار التفاعلي');
                const hasRemediation = reportHTML.includes('التوصيات للإصلاح');
                const hasVisualEvidence = reportHTML.includes('الأدلة البصرية الحقيقية');

                const reportLength = reportHTML.length;
                const imageCount = (reportHTML.match(/data:image\/png;base64,/g) || []).length;

                resultDiv.innerHTML = `✅ نتيجة اختبار formatSinglePageReport:

📊 محتوى التقرير:
<span class="status-indicator ${hasTitle ? 'status-success' : 'status-error'}"></span> عنوان شامل: ${hasTitle ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasVulnerabilities ? 'status-success' : 'status-error'}"></span> قسم الثغرات: ${hasVulnerabilities ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasImages ? 'status-success' : 'status-error'}"></span> الصور المدمجة: ${hasImages ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasExploitationSteps ? 'status-success' : 'status-error'}"></span> خطوات الاستغلال: ${hasExploitationSteps ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasImpactChanges ? 'status-success' : 'status-error'}"></span> تغيرات التأثير: ${hasImpactChanges ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasTechnicalDetails ? 'status-success' : 'status-error'}"></span> التفاصيل التقنية: ${hasTechnicalDetails ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasDialogue ? 'status-success' : 'status-error'}"></span> الحوار التفاعلي: ${hasDialogue ? 'موجود' : 'مفقود'}
<span class="status-indicator ${hasRemediation ? 'status-success' : 'status-error'}"></span> التوصيات: ${hasRemediation ? 'موجودة' : 'مفقودة'}
<span class="status-indicator ${hasVisualEvidence ? 'status-success' : 'status-error'}"></span> الأدلة البصرية: ${hasVisualEvidence ? 'موجودة' : 'مفقودة'}

📋 إحصائيات التقرير:
- طول التقرير: ${reportLength} حرف
- عدد الصور المدمجة: ${imageCount}
- عدد الثغرات: ${testVulnerabilities.length}

${hasTitle && hasVulnerabilities && hasImages && hasExploitationSteps && hasImpactChanges ?
  '🎉 formatSinglePageReport يعمل بشكل شامل ومكتمل!' :
  '⚠️ formatSinglePageReport يحتاج تحسين'}`;

                // عرض معاينة التقرير
                previewDiv.innerHTML = reportHTML.substring(0, 2000) + '...';
                previewDiv.style.display = 'block';

                resultDiv.className = hasTitle && hasVulnerabilities && hasImages && hasExploitationSteps && hasImpactChanges ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار تنسيق التقرير: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار ensureVulnerabilityImages
        async function testEnsureImages() {
            const resultDiv = document.getElementById('images-result');
            const imagesGrid = document.getElementById('images-grid');

            resultDiv.textContent = 'جاري اختبار ضمان الصور...';
            resultDiv.className = 'result info';
            imagesGrid.innerHTML = '';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('📸 اختبار ensureVulnerabilityImages...');

                // إنشاء ثغرة بدون صور للاختبار
                const vulnWithoutImages = {
                    name: 'Test_Vulnerability_Without_Images',
                    type: 'XSS',
                    severity: 'Medium',
                    location: 'https://example.com/test'
                };

                const testVulns = [...testVulnerabilities, vulnWithoutImages];

                // عد الصور قبل التشغيل
                const imagesBefore = bugBountyCore.countTotalScreenshots(testVulns);

                // تشغيل ensureVulnerabilityImages
                await bugBountyCore.ensureVulnerabilityImages(testVulns, 'https://example.com');

                // عد الصور بعد التشغيل
                const imagesAfter = bugBountyCore.countTotalScreenshots(testVulns);

                // التحقق من وجود الصور
                const vulnsWithImages = testVulns.filter(v => bugBountyCore.hasVulnerabilityImages(v)).length;
                const vulnsWithoutImages = testVulns.length - vulnsWithImages;

                resultDiv.innerHTML = `✅ نتيجة اختبار ensureVulnerabilityImages:

📊 إحصائيات الصور:
- الصور قبل التشغيل: ${imagesBefore}
- الصور بعد التشغيل: ${imagesAfter}
- الثغرات مع صور: ${vulnsWithImages}/${testVulns.length}
- الثغرات بدون صور: ${vulnsWithoutImages}

📸 تفاصيل الثغرات:
${testVulns.map((v, i) => `
- ${v.name}: ${bugBountyCore.hasVulnerabilityImages(v) ? '✅ لديها صور' : '❌ بدون صور'}`).join('')}

${vulnsWithImages === testVulns.length ?
  '🎉 ensureVulnerabilityImages يعمل بنجاح - جميع الثغرات لديها صور!' :
  '⚠️ ensureVulnerabilityImages يحتاج تحسين - بعض الثغرات بدون صور'}`;

                // عرض الصور في الشبكة
                testVulns.forEach(vuln => {
                    if (bugBountyCore.hasVulnerabilityImages(vuln)) {
                        const stages = ['before', 'during', 'after'];
                        stages.forEach(stage => {
                            const image = bugBountyCore.getVulnerabilityImage(vuln, stage);
                            if (image) {
                                const imageItem = document.createElement('div');
                                imageItem.className = 'image-item';
                                imageItem.innerHTML = `
                                    <h4>${vuln.name} - ${stage}</h4>
                                    <img src="data:image/png;base64,${image}" alt="${stage}">
                                    <p>✅ صورة متاحة</p>
                                `;
                                imagesGrid.appendChild(imageItem);
                            }
                        });
                    }
                });

                resultDiv.className = vulnsWithImages === testVulns.length ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار ضمان الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التكامل الكامل
        async function testFullIntegration() {
            const resultDiv = document.getElementById('integration-result');

            resultDiv.textContent = 'جاري اختبار التكامل الكامل...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('🔄 اختبار التكامل الكامل...');

                // 1. التحقق من صحة التقرير
                const validation = bugBountyCore.validateReportCompleteness(testVulnerabilities);

                // 2. ضمان وجود الصور
                await bugBountyCore.ensureVulnerabilityImages(testVulnerabilities, 'https://example.com');

                // 3. إنشاء التقرير الشامل
                const pageData = {
                    page_name: 'اختبار التكامل الكامل',
                    page_url: 'https://example.com/integration',
                    vulnerabilities: testVulnerabilities
                };

                const report = await bugBountyCore.formatSinglePageReport(pageData);

                // 4. تحليل النتائج
                const hasAllComponents = validation.isComplete &&
                                       report.includes('الأدلة البصرية الحقيقية') &&
                                       report.includes('خطوات الاستغلال') &&
                                       report.includes('التغيرات في النظام');

                resultDiv.innerHTML = `✅ نتيجة اختبار التكامل الكامل:

📊 مراحل التكامل:
<span class="status-indicator status-success"></span> تهيئة النظام: نجح
<span class="status-indicator ${validation.isComplete ? 'status-success' : 'status-error'}"></span> التحقق من التقرير: ${validation.isComplete ? 'نجح' : 'فشل'}
<span class="status-indicator status-success"></span> ضمان الصور: نجح
<span class="status-indicator ${report.length > 1000 ? 'status-success' : 'status-error'}"></span> إنشاء التقرير: ${report.length > 1000 ? 'نجح' : 'فشل'}

📋 تفاصيل التكامل:
- عدد الثغرات: ${testVulnerabilities.length}
- ثغرات مع صور: ${validation.vulnerabilitiesWithImages}
- ثغرات مع تفاصيل شاملة: ${validation.vulnerabilitiesWithExploitationSteps}
- طول التقرير النهائي: ${report.length} حرف

${hasAllComponents ?
  '🎉 التكامل الكامل يعمل بنجاح - النظام جاهز للإنتاج!' :
  '⚠️ التكامل يحتاج تحسين - راجع المراحل الفاشلة'}`;

                resultDiv.className = hasAllComponents ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التكامل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // إنشاء تقرير نهائي شامل
        async function generateComprehensiveReport() {
            const resultDiv = document.getElementById('final-result');

            resultDiv.textContent = 'جاري إنشاء التقرير النهائي...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await initializeSystemWithData();

                console.log('📄 إنشاء التقرير النهائي الشامل...');

                // إنشاء التقرير النهائي
                const finalPageData = {
                    page_name: 'التقرير النهائي الشامل',
                    page_url: 'https://example.com/final',
                    vulnerabilities: testVulnerabilities,
                    scan_summary: {
                        total_vulnerabilities: testVulnerabilities.length,
                        critical: testVulnerabilities.filter(v => v.severity === 'Critical').length,
                        high: testVulnerabilities.filter(v => v.severity === 'High').length,
                        medium: testVulnerabilities.filter(v => v.severity === 'Medium').length,
                        low: testVulnerabilities.filter(v => v.severity === 'Low').length
                    }
                };

                const finalReport = await bugBountyCore.formatSinglePageReport(finalPageData);

                // تحليل التقرير النهائي
                const hasComprehensiveContent = finalReport.includes('تقرير Bug Bounty الشامل') &&
                                              finalReport.includes('الأدلة البصرية الحقيقية') &&
                                              finalReport.includes('خطوات الاستغلال') &&
                                              finalReport.includes('التغيرات في النظام') &&
                                              finalReport.includes('التفاصيل التقنية') &&
                                              finalReport.includes('الحوار التفاعلي');

                const imageCount = (finalReport.match(/data:image\/png;base64,/g) || []).length;
                const reportSize = (finalReport.length / 1024).toFixed(2);

                // حفظ التقرير (محاكاة)
                const reportBlob = new Blob([finalReport], { type: 'text/html' });
                const reportUrl = URL.createObjectURL(reportBlob);

                resultDiv.innerHTML = `✅ التقرير النهائي الشامل:

📊 إحصائيات التقرير:
- حجم التقرير: ${reportSize} KB
- عدد الصور المدمجة: ${imageCount}
- عدد الثغرات: ${testVulnerabilities.length}
- محتوى شامل: ${hasComprehensiveContent ? '✅ متوفر' : '❌ ناقص'}

📋 محتويات التقرير:
<span class="status-indicator status-success"></span> العنوان والملخص
<span class="status-indicator status-success"></span> تفاصيل الثغرات الشاملة
<span class="status-indicator ${imageCount > 0 ? 'status-success' : 'status-error'}"></span> الصور الحقيقية (${imageCount})
<span class="status-indicator status-success"></span> خطوات الاستغلال
<span class="status-indicator status-success"></span> التغيرات والتأثيرات
<span class="status-indicator status-success"></span> التوصيات والإصلاحات

🔗 <a href="${reportUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📄 عرض التقرير النهائي</a>

${hasComprehensiveContent && imageCount > 0 ?
  '🎉 التقرير النهائي مكتمل وجاهز للتسليم!' :
  '⚠️ التقرير يحتاج مراجعة - بعض العناصر ناقصة'}`;

                resultDiv.className = hasComprehensiveContent && imageCount > 0 ? 'result success' : 'result warning';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في إنشاء التقرير النهائي: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🛡️ صفحة اختبار نظام التقارير الشامل جاهزة');
        });
    </script>
</body>
</html>
