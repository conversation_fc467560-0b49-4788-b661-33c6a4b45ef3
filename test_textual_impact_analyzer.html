<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار TextualImpactAnalyzer - v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .analysis-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 اختبار TextualImpactAnalyzer - v4.0</h1>
        <p><strong>الهدف:</strong> اختبار التحليل الشامل للتأثيرات الحقيقية للثغرات باستخدام TextualImpactAnalyzer</p>

        <div class="test-section">
            <h3>🔧 1. اختبار تحميل TextualImpactAnalyzer</h3>
            <button onclick="testTextualAnalyzerLoading()">اختبار التحميل</button>
            <div id="loading-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📝 2. اختبار التحليل النصي المتقدم</h3>
            <button onclick="testTextualAnalysis()">اختبار التحليل النصي</button>
            <div id="textual-analysis-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🌐 3. اختبار تحليل تغيرات DOM</h3>
            <button onclick="testDOMAnalysis()">اختبار تحليل DOM</button>
            <div id="dom-analysis-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🎭 4. اختبار التحليل السلوكي</h3>
            <button onclick="testBehavioralAnalysis()">اختبار التحليل السلوكي</button>
            <div id="behavioral-analysis-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>⚙️ 5. اختبار تحليل التأثير على النظام</h3>
            <button onclick="testSystemImpactAnalysis()">اختبار تأثير النظام</button>
            <div id="system-impact-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🎯 6. اختبار التكامل مع BugBountyCore</h3>
            <button onclick="testBugBountyCoreIntegration()">اختبار التكامل</button>
            <div id="integration-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let textualAnalyzer = null;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                console.log('🔧 تهيئة نظام Bug Bounty v4...');
                bugBountyCore = new BugBountyCore();
                
                // انتظار تحميل TextualImpactAnalyzer
                await bugBountyCore.initializeTextualImpactAnalyzer();
                
                console.log('✅ تم تهيئة النظام بنجاح');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                return false;
            }
        }

        // اختبار تحميل TextualImpactAnalyzer
        async function testTextualAnalyzerLoading() {
            const resultDiv = document.getElementById('loading-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // التحقق من وجود TextualImpactAnalyzer
                const isLoaded = typeof window.TextualImpactAnalyzer !== 'undefined';
                
                if (isLoaded) {
                    textualAnalyzer = new window.TextualImpactAnalyzer();
                    
                    resultDiv.innerHTML = `✅ نتيجة الاختبار:

<span class="status-indicator status-success"></span> TextualImpactAnalyzer محمل بنجاح
<span class="status-indicator status-success"></span> تم إنشاء instance جديد
<span class="status-indicator status-success"></span> الإصدار: ${textualAnalyzer.version}
<span class="status-indicator status-success"></span> اسم النظام: ${textualAnalyzer.systemName}

📊 المحركات المتاحة:
- محرك مقارنة DOM: ${textualAnalyzer.domComparisonEngine ? '✅ متاح' : '❌ غير متاح'}
- محرك التحليل السلوكي: ${textualAnalyzer.behavioralAnalysisEngine ? '✅ متاح' : '❌ غير متاح'}
- كاشف التغيرات النصية: ${textualAnalyzer.textualChangeDetector ? '✅ متاح' : '❌ غير متاح'}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ فشل في تحميل TextualImpactAnalyzer';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التحليل النصي المتقدم
        async function testTextualAnalysis() {
            const resultDiv = document.getElementById('textual-analysis-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!textualAnalyzer) {
                    await testTextualAnalyzerLoading();
                    if (!textualAnalyzer) throw new Error('TextualImpactAnalyzer غير متاح');
                }
                
                const testVuln = {
                    name: 'SQL Injection',
                    category: 'Injection',
                    severity: 'High'
                };
                
                const testExploitationResult = {
                    poc: {
                        evidence: 'MySQL error: You have an error in your SQL syntax',
                        response_snippet: 'Database connection failed with error code 1064',
                        payload: "' OR '1'='1' --"
                    },
                    success: true,
                    data_accessed: true
                };
                
                const websiteData = {
                    url: 'https://example.com/login',
                    forms: [{ action: '/login', method: 'POST' }]
                };

                console.log('🔧 اختبار performTextualAnalysis...');
                const textualAnalysis = await textualAnalyzer.performTextualAnalysis(
                    testVuln, 
                    websiteData, 
                    testExploitationResult
                );
                
                resultDiv.innerHTML = `✅ نتيجة التحليل النصي المتقدم:

<div class="analysis-content">
<h4>📝 التحليل النصي:</h4>
<p><strong>الوصف الديناميكي:</strong> ${textualAnalysis.description || 'غير متوفر'}</p>
<p><strong>Payloads المستخدمة:</strong> ${textualAnalysis.payloads_used?.join(', ') || 'غير متوفر'}</p>
<p><strong>رسائل الخطأ المكتشفة:</strong> ${textualAnalysis.error_messages_detected?.join(', ') || 'لا توجد'}</p>
<p><strong>كشف البيانات:</strong> ${textualAnalysis.data_exposure_detected || 'لا يوجد'}</p>
<p><strong>التغيرات النصية:</strong> ${textualAnalysis.textual_changes || 'لا توجد'}</p>
<p><strong>تأثيرات الحقن:</strong> ${textualAnalysis.injection_effects || 'لا توجد'}</p>
</div>

📊 إحصائيات:
- عدد Payloads: ${textualAnalysis.payloads_used?.length || 0}
- عدد رسائل الخطأ: ${textualAnalysis.error_messages_detected?.length || 0}
- جودة التحليل: ${textualAnalysis.description ? 'عالية' : 'منخفضة'}`;
                
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار تحليل DOM
        async function testDOMAnalysis() {
            const resultDiv = document.getElementById('dom-analysis-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!textualAnalyzer) {
                    await testTextualAnalyzerLoading();
                    if (!textualAnalyzer) throw new Error('TextualImpactAnalyzer غير متاح');
                }
                
                const testVuln = {
                    name: 'XSS',
                    category: 'Injection',
                    severity: 'Medium'
                };
                
                const testExploitationResult = {
                    dom_changes: ['تم إضافة عنصر script جديد', 'تم تعديل innerHTML'],
                    new_elements: ['<script>alert("XSS")</script>'],
                    modified_elements: ['input field value changed']
                };
                
                const websiteData = {
                    url: 'https://example.com/search',
                    forms: [{ action: '/search', method: 'GET' }]
                };

                console.log('🔧 اختبار performDOMAnalysis...');
                const domAnalysis = await textualAnalyzer.performDOMAnalysis(
                    testVuln, 
                    websiteData, 
                    testExploitationResult
                );
                
                resultDiv.innerHTML = `✅ نتيجة تحليل DOM:

<div class="analysis-content">
<h4>🌐 تحليل تغيرات DOM:</h4>
<p><strong>تغيرات DOM:</strong> ${domAnalysis.dom_changes || 'لا توجد'}</p>
<p><strong>عناصر جديدة:</strong> ${domAnalysis.new_elements_added || 'لا توجد'}</p>
<p><strong>عناصر معدلة:</strong> ${domAnalysis.elements_modified || 'لا توجد'}</p>
<p><strong>عناصر محذوفة:</strong> ${domAnalysis.elements_removed || 'لا توجد'}</p>
<p><strong>تغيرات التخطيط:</strong> ${domAnalysis.layout_changes || 'لا توجد'}</p>
<p><strong>تعديلات الأنماط:</strong> ${domAnalysis.style_modifications || 'لا توجد'}</p>
</div>

📊 إحصائيات DOM:
- تم رصد تغيرات: ${domAnalysis.dom_changes ? 'نعم' : 'لا'}
- عناصر جديدة: ${domAnalysis.new_elements_added ? 'نعم' : 'لا'}
- تعديلات الأنماط: ${domAnalysis.style_modifications ? 'نعم' : 'لا'}`;
                
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التحليل السلوكي
        async function testBehavioralAnalysis() {
            const resultDiv = document.getElementById('behavioral-analysis-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!textualAnalyzer) {
                    await testTextualAnalyzerLoading();
                    if (!textualAnalyzer) throw new Error('TextualImpactAnalyzer غير متاح');
                }
                
                const testVuln = {
                    name: 'Authentication Bypass',
                    category: 'Authentication',
                    severity: 'Critical'
                };
                
                const testExploitationResult = {
                    response_time_before: 200,
                    response_time_after: 150,
                    session_hijacked: true,
                    authentication_bypassed: true
                };
                
                const websiteData = {
                    url: 'https://example.com/admin',
                    authentication_required: true
                };

                console.log('🔧 اختبار performBehavioralAnalysis...');
                const behavioralAnalysis = await textualAnalyzer.performBehavioralAnalysis(
                    testVuln, 
                    websiteData, 
                    testExploitationResult
                );
                
                resultDiv.innerHTML = `✅ نتيجة التحليل السلوكي:

<div class="analysis-content">
<h4>🎭 التحليل السلوكي:</h4>
<p><strong>تغيرات وقت الاستجابة:</strong> ${behavioralAnalysis.response_time_changes || 'لا توجد'}</p>
<p><strong>تغيرات سلوك الخادم:</strong> ${behavioralAnalysis.server_behavior_changes || 'لا توجد'}</p>
<p><strong>تأثير الجلسة:</strong> ${behavioralAnalysis.session_impact || 'لا يوجد'}</p>
<p><strong>تجاوز المصادقة:</strong> ${behavioralAnalysis.authentication_bypass || 'لا يوجد'}</p>
<p><strong>تغيرات التخويل:</strong> ${behavioralAnalysis.authorization_changes || 'لا توجد'}</p>
<p><strong>تأثير الأداء:</strong> ${behavioralAnalysis.performance_impact || 'لا يوجد'}</p>
</div>

📊 تقييم السلوك:
- تم تجاوز المصادقة: ${testExploitationResult.authentication_bypassed ? 'نعم' : 'لا'}
- تم اختطاف الجلسة: ${testExploitationResult.session_hijacked ? 'نعم' : 'لا'}
- تغيير في الأداء: ${testExploitationResult.response_time_before !== testExploitationResult.response_time_after ? 'نعم' : 'لا'}`;
                
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار تحليل التأثير على النظام
        async function testSystemImpactAnalysis() {
            const resultDiv = document.getElementById('system-impact-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!textualAnalyzer) {
                    await testTextualAnalyzerLoading();
                    if (!textualAnalyzer) throw new Error('TextualImpactAnalyzer غير متاح');
                }
                
                const testVuln = {
                    name: 'Command Injection',
                    category: 'Injection',
                    severity: 'Critical'
                };
                
                const testExploitationResult = {
                    code_executed: true,
                    file_system_accessed: true,
                    database_compromised: false,
                    network_access: true
                };
                
                const websiteData = {
                    url: 'https://example.com/exec',
                    server_info: 'Apache/2.4.41'
                };

                console.log('🔧 اختبار performSystemImpactAnalysis...');
                const systemImpact = await textualAnalyzer.performSystemImpactAnalysis(
                    testVuln, 
                    websiteData, 
                    testExploitationResult
                );
                
                resultDiv.innerHTML = `✅ نتيجة تحليل التأثير على النظام:

<div class="analysis-content">
<h4>⚙️ تحليل التأثير على النظام:</h4>
<p><strong>تأثير Backend:</strong> ${systemImpact.backend_impact || 'لا يوجد'}</p>
<p><strong>تأثير قاعدة البيانات:</strong> ${systemImpact.database_impact || 'لا يوجد'}</p>
<p><strong>تأثير نظام الملفات:</strong> ${systemImpact.file_system_impact || 'لا يوجد'}</p>
<p><strong>تأثير الشبكة:</strong> ${systemImpact.network_impact || 'لا يوجد'}</p>
<p><strong>الضوابط الأمنية المتجاوزة:</strong> ${systemImpact.security_controls_bypassed || 'لا توجد'}</p>
<p><strong>تأثير سلامة البيانات:</strong> ${systemImpact.data_integrity_impact || 'لا يوجد'}</p>
</div>

📊 تقييم التأثير:
- تم تنفيذ كود: ${testExploitationResult.code_executed ? 'نعم' : 'لا'}
- تم الوصول لنظام الملفات: ${testExploitationResult.file_system_accessed ? 'نعم' : 'لا'}
- تم اختراق قاعدة البيانات: ${testExploitationResult.database_compromised ? 'نعم' : 'لا'}
- تم الوصول للشبكة: ${testExploitationResult.network_access ? 'نعم' : 'لا'}`;
                
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التكامل مع BugBountyCore
        async function testBugBountyCoreIntegration() {
            const resultDiv = document.getElementById('integration-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'SQL Injection',
                    severity: 'High',
                    location: 'https://example.com/login',
                    exploitation_result: {
                        poc: {
                            evidence: 'MySQL error detected',
                            response_snippet: 'Database error occurred',
                            payload: "' OR '1'='1' --"
                        },
                        success: true
                    }
                };
                
                const realData = {
                    injection_point: '/login?id=',
                    payload: "' OR '1'='1' --",
                    exploitation_result: 'تم تجاوز المصادقة بنجاح'
                };

                console.log('🔧 اختبار generateComprehensiveDetailsFromRealData...');
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                const hasTextualAnalysis = comprehensiveDetails.includes('التحليل النصي المتقدم');
                const hasDOMAnalysis = comprehensiveDetails.includes('تحليل تغيرات DOM');
                const hasBehavioralAnalysis = comprehensiveDetails.includes('التحليل السلوكي');
                const hasSystemImpact = comprehensiveDetails.includes('تحليل التأثير على النظام');
                
                resultDiv.innerHTML = `✅ نتيجة اختبار التكامل:

📊 التحقق من التكامل:
<span class="status-indicator ${hasTextualAnalysis ? 'status-success' : 'status-error'}"></span> التحليل النصي المتقدم: ${hasTextualAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasDOMAnalysis ? 'status-success' : 'status-error'}"></span> تحليل تغيرات DOM: ${hasDOMAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasBehavioralAnalysis ? 'status-success' : 'status-error'}"></span> التحليل السلوكي: ${hasBehavioralAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasSystemImpact ? 'status-success' : 'status-error'}"></span> تحليل التأثير على النظام: ${hasSystemImpact ? 'متاح' : 'غير متاح'}

📋 طول التفاصيل الشاملة: ${comprehensiveDetails.length} حرف
📋 يحتوي على HTML: ${comprehensiveDetails.includes('<') ? 'نعم' : 'لا'}

${hasTextualAnalysis && hasDOMAnalysis && hasBehavioralAnalysis && hasSystemImpact ? 
  '🎉 التكامل يعمل بنجاح! جميع أنواع التحليل متاحة.' : 
  '⚠️ التكامل يحتاج إصلاح - بعض أنواع التحليل مفقودة.'}`;
                
                resultDiv.className = hasTextualAnalysis && hasDOMAnalysis ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
