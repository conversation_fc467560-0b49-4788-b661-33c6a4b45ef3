<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور والتفاصيل الحقيقية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .result {
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            max-height: 600px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }
        .result.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            color: #155724;
        }
        .result.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border: 2px solid #dc3545;
            color: #721c24;
        }
        .result.info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border: 2px solid #17a2b8;
            color: #0c5460;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
        }
        .report-output {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            max-height: 500px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 اختبار الصور والتفاصيل الحقيقية</h1>
            <p>اختبار مباشر للتحديثات الجديدة: formatComprehensiveVulnerabilitySection + الصور الفعلية</p>
        </div>

        <div class="test-section">
            <h3>🔧 1. إنشاء ثغرة مع بيانات حقيقية</h3>
            <button onclick="createRealVulnerability()">إنشاء ثغرة حقيقية</button>
            <div id="create-result" class="result info">انقر على الزر لإنشاء ثغرة مع بيانات حقيقية...</div>
        </div>

        <div class="test-section">
            <h3>📋 2. اختبار formatComprehensiveVulnerabilitySection</h3>
            <button onclick="testFormatComprehensive()">اختبار التنسيق الشامل</button>
            <div id="format-result" class="result info">انقر على الزر لاختبار التنسيق...</div>
            <div id="format-output" class="report-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📸 3. اختبار الصور الحقيقية</h3>
            <button onclick="testRealImages()">اختبار الصور الحقيقية</button>
            <div id="images-result" class="result info">انقر على الزر لاختبار الصور...</div>
        </div>

        <div class="test-section">
            <h3>🎯 4. اختبار generateVulnerabilitiesHTML الجديد</h3>
            <button onclick="testNewGenerateHTML()">اختبار HTML الجديد</button>
            <div id="html-result" class="result info">انقر على الزر لاختبار HTML الجديد...</div>
            <div id="html-output" class="report-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🚀 5. اختبار التقرير الكامل</h3>
            <button onclick="testFullReport()">إنشاء تقرير كامل</button>
            <div id="full-result" class="result info">انقر على الزر لإنشاء تقرير كامل...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testVulnerability = null;

        // إنشاء ثغرة مع بيانات حقيقية
        async function createRealVulnerability() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.textContent = 'جاري إنشاء ثغرة مع بيانات حقيقية...';
            resultDiv.className = 'result info';
            
            try {
                // تهيئة النظام
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    bugBountyCore.analysisState = {
                        reportId: `real_test_${Date.now()}`,
                        vulnerabilities: [],
                        websiteData: { url: 'https://example.com' }
                    };
                }
                
                // إنشاء ثغرة مع جميع البيانات الحقيقية المطلوبة
                testVulnerability = {
                    name: 'Real_SQL_Injection_Test',
                    type: 'SQL Injection',
                    category: 'Injection',
                    severity: 'Critical',
                    cvss_score: 9.8,
                    description: 'ثغرة SQL Injection حقيقية تم اكتشافها واختبارها في النظام المستهدف',
                    location: 'https://example.com/login',
                    impact: 'يمكن للمهاجم الوصول الكامل لقاعدة البيانات وسرقة جميع البيانات الحساسة',
                    remediation: 'استخدام prepared statements وparameter binding وتطبيق input validation',
                    cwe: 'CWE-89',
                    owasp: 'A03:2021 – Injection',
                    
                    // البيانات الحقيقية المطلوبة
                    exploitation_result: 'تم الوصول بنجاح لقاعدة البيانات وقراءة جدول المستخدمين. تم استخراج 150 سجل مستخدم مع كلمات المرور المشفرة.',
                    exploitation_steps: `1. إدخال payload في حقل username: admin' OR '1'='1' --
2. تجاوز نظام المصادقة بنجاح
3. الوصول لصفحة الإدارة
4. تنفيذ استعلام UNION لاستخراج البيانات
5. تحميل قائمة المستخدمين الكاملة`,
                    impact_changes: 'تجاوز نظام المصادقة، الوصول غير المصرح به لصفحة الإدارة، استخراج بيانات المستخدمين، كشف هيكل قاعدة البيانات، إمكانية تعديل وحذف البيانات',
                    technical_details: {
                        payload: "admin' OR '1'='1' --",
                        vulnerable_parameter: 'username',
                        response_code: 200,
                        database_error: 'MySQL syntax error detected',
                        extracted_data: 'users table: 150 records',
                        injection_type: 'Boolean-based blind',
                        database_version: 'MySQL 5.7.34'
                    },
                    dialogue: 'تم اكتشاف ثغرة SQL Injection خطيرة جداً تتطلب إصلاح عاجل. هذه الثغرة تسمح للمهاجمين بالوصول الكامل لقاعدة البيانات وسرقة جميع المعلومات الحساسة.',
                    
                    // الصور الحقيقية (base64 مبسط للاختبار)
                    visual_proof: {
                        before_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                        during_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                        after_screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8////fwAJAQMDeaKOyQAAAABJRU5ErkJggg=='
                    },
                    images: {
                        before: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                        during: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                        after: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8////fwAJAQMDeaKOyQAAAABJRU5ErkJggg=='
                    }
                };
                
                // التحقق من وجود جميع البيانات المطلوبة
                const hasExploitationResult = !!testVulnerability.exploitation_result;
                const hasExploitationSteps = !!testVulnerability.exploitation_steps;
                const hasImpactChanges = !!testVulnerability.impact_changes;
                const hasTechnicalDetails = !!testVulnerability.technical_details;
                const hasDialogue = !!testVulnerability.dialogue;
                const hasImages = !!testVulnerability.visual_proof && !!testVulnerability.images;
                
                resultDiv.innerHTML = `✅ تم إنشاء ثغرة مع بيانات حقيقية:

📋 البيانات المتوفرة:
✅ exploitation_result: ${hasExploitationResult ? 'متوفر' : 'مفقود'}
✅ exploitation_steps: ${hasExploitationSteps ? 'متوفر' : 'مفقود'}
✅ impact_changes: ${hasImpactChanges ? 'متوفر' : 'مفقود'}
✅ technical_details: ${hasTechnicalDetails ? 'متوفر' : 'مفقود'}
✅ dialogue: ${hasDialogue ? 'متوفر' : 'مفقود'}
✅ الصور: ${hasImages ? 'متوفرة (3 صور)' : 'مفقودة'}

📊 تفاصيل الثغرة:
- الاسم: ${testVulnerability.name}
- النوع: ${testVulnerability.type}
- الخطورة: ${testVulnerability.severity}
- CWE: ${testVulnerability.cwe}
- OWASP: ${testVulnerability.owasp}

🎉 الثغرة جاهزة للاختبار مع جميع البيانات المطلوبة!`;
                
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في إنشاء الثغرة: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار formatComprehensiveVulnerabilitySection
        async function testFormatComprehensive() {
            const resultDiv = document.getElementById('format-result');
            const outputDiv = document.getElementById('format-output');
            
            resultDiv.textContent = 'جاري اختبار التنسيق الشامل...';
            resultDiv.className = 'result info';
            outputDiv.style.display = 'none';
            
            try {
                if (!testVulnerability) await createRealVulnerability();
                
                console.log('📋 اختبار formatComprehensiveVulnerabilitySection...');
                
                // تشغيل الدالة الجديدة
                const comprehensiveHTML = bugBountyCore.formatComprehensiveVulnerabilitySection(
                    testVulnerability, 
                    1, 
                    'https://example.com'
                );
                
                // تحليل المحتوى
                const hasTitle = comprehensiveHTML.includes(testVulnerability.name);
                const hasExploitationResult = comprehensiveHTML.includes('نتائج الاستغلال الفعلي');
                const hasExploitationSteps = comprehensiveHTML.includes('خطوات الاستغلال الحقيقية');
                const hasImpactChanges = comprehensiveHTML.includes('التغيرات الحقيقية في النظام');
                const hasTechnicalDetails = comprehensiveHTML.includes('التفاصيل التقنية الحقيقية');
                const hasDialogue = comprehensiveHTML.includes('الحوار التفاعلي الحقيقي');
                const hasImages = comprehensiveHTML.includes('data:image/png;base64,');
                const hasRealData = comprehensiveHTML.includes(testVulnerability.exploitation_result.substring(0, 20));
                
                resultDiv.innerHTML = `✅ نتيجة اختبار formatComprehensiveVulnerabilitySection:

📊 المحتوى المولد:
✅ عنوان الثغرة: ${hasTitle ? 'موجود' : 'مفقود'}
✅ نتائج الاستغلال: ${hasExploitationResult ? 'موجود' : 'مفقود'}
✅ خطوات الاستغلال: ${hasExploitationSteps ? 'موجود' : 'مفقود'}
✅ تغيرات التأثير: ${hasImpactChanges ? 'موجود' : 'مفقود'}
✅ التفاصيل التقنية: ${hasTechnicalDetails ? 'موجود' : 'مفقود'}
✅ الحوار التفاعلي: ${hasDialogue ? 'موجود' : 'مفقود'}
✅ الصور الفعلية: ${hasImages ? 'موجودة' : 'مفقودة'}
✅ البيانات الحقيقية: ${hasRealData ? 'موجودة' : 'مفقودة'}

📋 إحصائيات:
- طول المحتوى: ${comprehensiveHTML.length} حرف
- عدد الصور: ${(comprehensiveHTML.match(/data:image\/png;base64,/g) || []).length}

${hasTitle && hasExploitationResult && hasExploitationSteps && hasImages && hasRealData ? 
  '🎉 formatComprehensiveVulnerabilitySection يعمل بنجاح!' : 
  '⚠️ formatComprehensiveVulnerabilitySection يحتاج مراجعة'}`;
                
                // عرض المحتوى المولد
                outputDiv.innerHTML = comprehensiveHTML;
                outputDiv.style.display = 'block';
                
                resultDiv.className = hasTitle && hasExploitationResult && hasExploitationSteps && hasImages && hasRealData ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التنسيق: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🔬 صفحة اختبار الصور والتفاصيل الحقيقية جاهزة');
        });
    </script>
</body>
</html>
