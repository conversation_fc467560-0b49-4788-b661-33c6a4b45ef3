<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي الشامل - النظام v4</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }
        .test-section:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.2);
        }
        .result {
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }
        .result.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            color: #155724;
        }
        .result.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border: 2px solid #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            color: #856404;
        }
        .result.info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border: 2px solid #17a2b8;
            color: #0c5460;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        button:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .status-indicator {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-left: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .status-success { background: linear-gradient(135deg, #28a745, #20c997); }
        .status-error { background: linear-gradient(135deg, #dc3545, #e74c3c); }
        .status-warning { background: linear-gradient(135deg, #ffc107, #f39c12); }
        .screenshot-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .screenshot-item {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .screenshot-item:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .screenshot-item img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .screenshot-item h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            border-color: #007bff;
            transform: translateY(-3px);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ الاختبار النهائي الشامل - النظام v4</h1>
            <p>اختبار شامل لجميع الإصلاحات: التفاصيل الحقيقية + الصور مع التأثيرات + التقارير الشاملة</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overall-progress"></div>
            </div>
        </div>

        <div class="stats-grid" id="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="tests-completed">0</div>
                <div class="stat-label">اختبارات مكتملة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tests-passed">0</div>
                <div class="stat-label">اختبارات ناجحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="vulnerabilities-tested">0</div>
                <div class="stat-label">ثغرات مختبرة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="screenshots-captured">0</div>
                <div class="stat-label">صور ملتقطة</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 1. تهيئة النظام الشامل</h3>
            <button onclick="runSystemInitialization()">تهيئة النظام</button>
            <div id="init-result" class="result info">انقر على الزر لبدء التهيئة الشاملة...</div>
        </div>

        <div class="test-section">
            <h3>🎯 2. اختبار الثغرات مع التأثيرات البصرية</h3>
            <button onclick="runVulnerabilityTestsWithEffects()">اختبار الثغرات مع التأثيرات</button>
            <div id="vuln-effects-result" class="result info">انقر على الزر لبدء اختبار الثغرات...</div>
            <div id="vuln-effects-screenshots" class="screenshot-preview"></div>
        </div>

        <div class="test-section">
            <h3>📊 3. اختبار التحليل الشامل</h3>
            <button onclick="runComprehensiveAnalysis()">اختبار التحليل الشامل</button>
            <div id="analysis-result" class="result info">انقر على الزر لبدء التحليل...</div>
        </div>

        <div class="test-section">
            <h3>📋 4. اختبار التقارير النهائية</h3>
            <button onclick="runFinalReportTest()">اختبار التقارير النهائية</button>
            <div id="report-result" class="result info">انقر على الزر لبدء اختبار التقارير...</div>
        </div>

        <div class="test-section">
            <h3>🔄 5. اختبار السيناريو الكامل</h3>
            <button onclick="runFullScenarioTest()">تشغيل السيناريو الكامل</button>
            <div id="full-scenario-result" class="result info">انقر على الزر لبدء السيناريو الكامل...</div>
        </div>

        <div class="test-section">
            <h3>✅ 6. النتيجة النهائية</h3>
            <button onclick="generateFinalReport()">إنشاء التقرير النهائي</button>
            <div id="final-report-result" class="result info">انقر على الزر لإنشاء التقرير النهائي...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testStats = {
            testsCompleted: 0,
            testsPassed: 0,
            vulnerabilitiesTested: 0,
            screenshotsCaptured: 0,
            totalTests: 6
        };

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('tests-completed').textContent = testStats.testsCompleted;
            document.getElementById('tests-passed').textContent = testStats.testsPassed;
            document.getElementById('vulnerabilities-tested').textContent = testStats.vulnerabilitiesTested;
            document.getElementById('screenshots-captured').textContent = testStats.screenshotsCaptured;
            
            const progress = (testStats.testsCompleted / testStats.totalTests) * 100;
            document.getElementById('overall-progress').style.width = `${progress}%`;
        }

        // تهيئة النظام الشامل
        async function runSystemInitialization() {
            const resultDiv = document.getElementById('init-result');
            resultDiv.textContent = 'جاري التهيئة الشاملة...';
            resultDiv.className = 'result info';
            
            try {
                console.log('🔧 بدء التهيئة الشاملة للنظام v4...');
                
                // تهيئة BugBountyCore
                bugBountyCore = new BugBountyCore();
                
                // تهيئة analysisState
                bugBountyCore.analysisState = {
                    reportId: `final_test_${Date.now()}`,
                    vulnerabilities: [],
                    websiteData: { url: 'https://example.com' },
                    vulnerabilityScreenshots: {},
                    testResults: []
                };
                
                // تهيئة TextualImpactAnalyzer
                await bugBountyCore.initializeTextualImpactAnalyzer();
                
                // تهيئة ImpactVisualizer
                await bugBountyCore.initializeImpactVisualizer();
                
                // تهيئة Python Screenshot Service
                await bugBountyCore.initializePythonScreenshotService();
                
                // التحقق من جميع المكونات
                const hasCore = bugBountyCore !== null;
                const hasTextualAnalyzer = bugBountyCore.textualImpactAnalyzer !== null;
                const hasImpactVisualizer = bugBountyCore.impactVisualizer !== null;
                const hasPythonService = bugBountyCore.pythonBridge !== null;
                const hasAnalysisState = bugBountyCore.analysisState !== null;
                
                const allComponentsReady = hasCore && hasTextualAnalyzer && hasImpactVisualizer && hasAnalysisState;
                
                resultDiv.innerHTML = `✅ نتيجة التهيئة الشاملة:

📊 المكونات الأساسية:
<span class="status-indicator ${hasCore ? 'status-success' : 'status-error'}"></span> BugBountyCore: ${hasCore ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasTextualAnalyzer ? 'status-success' : 'status-error'}"></span> TextualImpactAnalyzer: ${hasTextualAnalyzer ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasImpactVisualizer ? 'status-success' : 'status-error'}"></span> ImpactVisualizer: ${hasImpactVisualizer ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasPythonService ? 'status-success' : 'status-warning'}"></span> Python Service: ${hasPythonService ? 'متاح' : 'سيتم تهيئته لاحقاً'}
<span class="status-indicator ${hasAnalysisState ? 'status-success' : 'status-error'}"></span> AnalysisState: ${hasAnalysisState ? 'متاح' : 'غير متاح'}

📋 معلومات النظام:
- معرف التقرير: ${bugBountyCore.analysisState.reportId}
- إصدار النظام: v4.0
- وقت التهيئة: ${new Date().toLocaleString()}

🔧 الدوال المتاحة:
- generateRealVulnerabilityDetails: متاحة
- captureVulnerabilityScreenshotsDuringTesting: متاحة
- applyVulnerabilityToPage: متاحة
- createExploitationResultPage: متاحة
- generateVulnerabilitiesHTML: متاحة

${allComponentsReady ? 
  '🎉 النظام v4 جاهز بالكامل للاختبار الشامل!' : 
  '⚠️ بعض المكونات تحتاج إصلاح'}`;
                
                resultDiv.className = allComponentsReady ? 'result success' : 'result warning';
                
                if (allComponentsReady) {
                    testStats.testsPassed++;
                }
                testStats.testsCompleted++;
                updateStats();
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في التهيئة الشاملة: ${error.message}`;
                resultDiv.className = 'result error';
                testStats.testsCompleted++;
                updateStats();
            }
        }

        // اختبار الثغرات مع التأثيرات البصرية
        async function runVulnerabilityTestsWithEffects() {
            const resultDiv = document.getElementById('vuln-effects-result');
            const screenshotsDiv = document.getElementById('vuln-effects-screenshots');

            resultDiv.textContent = 'جاري اختبار الثغرات مع التأثيرات...';
            resultDiv.className = 'result info';
            screenshotsDiv.innerHTML = '';

            try {
                if (!bugBountyCore) await runSystemInitialization();

                // إنشاء ثغرات متنوعة للاختبار
                const testVulnerabilities = [
                    {
                        name: 'Advanced_XSS_Attack',
                        category: 'Injection',
                        severity: 'High',
                        location: 'https://example.com/xss',
                        payload: "<script>alert('Advanced XSS')</script>",
                        visual_proof: {}
                    },
                    {
                        name: 'Critical_SQL_Injection',
                        category: 'Injection',
                        severity: 'Critical',
                        location: 'https://example.com/sql',
                        payload: "' UNION SELECT * FROM users --",
                        visual_proof: {}
                    },
                    {
                        name: 'Local_File_Inclusion_Attack',
                        category: 'File Inclusion',
                        severity: 'High',
                        location: 'https://example.com/lfi',
                        payload: "../../../etc/passwd",
                        visual_proof: {}
                    }
                ];

                console.log('🎯 بدء اختبار الثغرات مع التأثيرات البصرية...');

                let successfulTests = 0;
                let totalScreenshots = 0;

                // اختبار كل ثغرة
                for (const vuln of testVulnerabilities) {
                    try {
                        console.log(`🔍 اختبار الثغرة: ${vuln.name}`);

                        // تطبيق التفاصيل الحقيقية مع التقاط الصور
                        await bugBountyCore.generateRealVulnerabilityDetails(vuln, null, vuln.location);

                        // التحقق من الصور
                        const hasAllScreenshots = vuln.visual_proof?.before_screenshot &&
                                                 vuln.visual_proof?.during_screenshot &&
                                                 vuln.visual_proof?.after_screenshot;

                        if (hasAllScreenshots) {
                            successfulTests++;
                            totalScreenshots += 3;

                            // عرض الصور
                            displayVulnerabilityScreenshots(screenshotsDiv, vuln);
                        }

                        testStats.vulnerabilitiesTested++;

                    } catch (error) {
                        console.error(`❌ خطأ في اختبار الثغرة ${vuln.name}:`, error);
                    }
                }

                // إضافة الثغرات إلى analysisState
                bugBountyCore.analysisState.vulnerabilities = testVulnerabilities;
                testStats.screenshotsCaptured += totalScreenshots;

                const successRate = (successfulTests / testVulnerabilities.length) * 100;

                resultDiv.innerHTML = `✅ نتيجة اختبار الثغرات مع التأثيرات:

📊 إحصائيات الاختبار:
- عدد الثغرات المختبرة: ${testVulnerabilities.length}
- اختبارات ناجحة: ${successfulTests}
- معدل النجاح: ${successRate.toFixed(1)}%
- إجمالي الصور: ${totalScreenshots}

📸 تفاصيل الصور:
<span class="status-indicator ${totalScreenshots >= 9 ? 'status-success' : 'status-error'}"></span> صور قبل الاستغلال: ${testVulnerabilities.filter(v => v.visual_proof?.before_screenshot).length}
<span class="status-indicator ${totalScreenshots >= 9 ? 'status-success' : 'status-error'}"></span> صور أثناء الاستغلال: ${testVulnerabilities.filter(v => v.visual_proof?.during_screenshot).length}
<span class="status-indicator ${totalScreenshots >= 9 ? 'status-success' : 'status-error'}"></span> صور بعد الاستغلال: ${testVulnerabilities.filter(v => v.visual_proof?.after_screenshot).length}

🎯 أنواع الثغرات المختبرة:
- XSS: ${testVulnerabilities.filter(v => v.name.includes('XSS')).length}
- SQL Injection: ${testVulnerabilities.filter(v => v.name.includes('SQL')).length}
- LFI: ${testVulnerabilities.filter(v => v.name.includes('LFI')).length}

${successfulTests === testVulnerabilities.length ?
  '🎉 جميع الثغرات تم اختبارها بنجاح مع التأثيرات البصرية!' :
  '⚠️ بعض الثغرات تحتاج إصلاح - تحقق من Python Service'}`;

                resultDiv.className = successfulTests === testVulnerabilities.length ? 'result success' : 'result warning';

                if (successfulTests === testVulnerabilities.length) {
                    testStats.testsPassed++;
                }
                testStats.testsCompleted++;
                updateStats();

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار الثغرات: ${error.message}`;
                resultDiv.className = 'result error';
                testStats.testsCompleted++;
                updateStats();
            }
        }

        // عرض صور الثغرة
        function displayVulnerabilityScreenshots(container, vulnerability) {
            const stages = ['before', 'during', 'after'];
            const stageNames = {
                'before': 'قبل الاستغلال',
                'during': 'أثناء الاستغلال',
                'after': 'بعد الاستغلال'
            };
            const stageIcons = {
                'before': '📷',
                'during': '⚡',
                'after': '🚨'
            };

            stages.forEach(stage => {
                const screenshot = vulnerability.visual_proof?.[`${stage}_screenshot`];

                const screenshotItem = document.createElement('div');
                screenshotItem.className = 'screenshot-item';

                if (screenshot) {
                    screenshotItem.innerHTML = `
                        <h4>${stageIcons[stage]} ${stageNames[stage]} - ${vulnerability.name}</h4>
                        <img src="data:image/png;base64,${screenshot}" alt="${stage} screenshot">
                        <p style="font-size: 12px; color: #28a745; margin: 10px 0 0 0; font-weight: bold;">
                            ✅ صورة حقيقية مع تأثيرات - ${new Date().toLocaleTimeString()}
                        </p>
                        <p style="font-size: 11px; color: #6c757d; margin: 5px 0 0 0;">
                            Payload: ${vulnerability.payload}
                        </p>
                    `;
                } else {
                    screenshotItem.innerHTML = `
                        <h4>${stageIcons[stage]} ${stageNames[stage]} - ${vulnerability.name}</h4>
                        <div style="background: #f8f9fa; border: 2px dashed #dee2e6; padding: 40px; text-align: center; border-radius: 8px;">
                            <p style="color: #6c757d; margin: 0; font-size: 16px;">📸 صورة غير متاحة</p>
                        </div>
                        <p style="font-size: 12px; color: #dc3545; margin: 10px 0 0 0; font-weight: bold;">
                            ❌ فشل في التقاط الصورة
                        </p>
                    `;
                }

                container.appendChild(screenshotItem);
            });
        }

        // اختبار التحليل الشامل
        async function runComprehensiveAnalysis() {
            const resultDiv = document.getElementById('analysis-result');

            resultDiv.textContent = 'جاري اختبار التحليل الشامل...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await runSystemInitialization();

                // استخدام الثغرات من الاختبار السابق أو إنشاء ثغرة جديدة
                let testVulnerabilities = bugBountyCore.analysisState?.vulnerabilities || [];

                if (testVulnerabilities.length === 0) {
                    testVulnerabilities = [{
                        name: 'Analysis_Test_Vulnerability',
                        category: 'Injection',
                        severity: 'High',
                        location: 'https://example.com/analysis',
                        payload: "test_analysis_payload",
                        visual_proof: {}
                    }];

                    // تطبيق التفاصيل الحقيقية
                    await bugBountyCore.generateRealVulnerabilityDetails(testVulnerabilities[0], null, testVulnerabilities[0].location);
                }

                console.log('🔬 بدء التحليل الشامل...');

                let analysisResults = [];

                // تحليل كل ثغرة
                for (const vuln of testVulnerabilities) {
                    try {
                        if (bugBountyCore.textualImpactAnalyzer) {
                            const analysis = await bugBountyCore.textualImpactAnalyzer.analyzeVulnerabilityDetails(
                                vuln,
                                { url: vuln.location },
                                vuln.exploitation_result || {}
                            );

                            analysisResults.push({
                                vulnerability: vuln.name,
                                analysis: analysis,
                                hasTextualAnalysis: !!analysis?.textual_analysis,
                                hasDOMAnalysis: !!analysis?.dom_analysis,
                                hasBehavioralAnalysis: !!analysis?.behavioral_analysis,
                                hasSystemImpact: !!analysis?.system_impact_analysis
                            });
                        }
                    } catch (error) {
                        console.error(`❌ خطأ في تحليل الثغرة ${vuln.name}:`, error);
                    }
                }

                const successfulAnalyses = analysisResults.filter(r =>
                    r.hasTextualAnalysis && r.hasDOMAnalysis && r.hasBehavioralAnalysis && r.hasSystemImpact
                ).length;

                const analysisRate = (successfulAnalyses / analysisResults.length) * 100;

                resultDiv.innerHTML = `✅ نتيجة التحليل الشامل:

📊 إحصائيات التحليل:
- عدد الثغرات المحللة: ${analysisResults.length}
- تحليلات شاملة ناجحة: ${successfulAnalyses}
- معدل نجاح التحليل: ${analysisRate.toFixed(1)}%

📋 أنواع التحليل المتاحة:
<span class="status-indicator ${analysisResults.filter(r => r.hasTextualAnalysis).length > 0 ? 'status-success' : 'status-error'}"></span> التحليل النصي: ${analysisResults.filter(r => r.hasTextualAnalysis).length}/${analysisResults.length}
<span class="status-indicator ${analysisResults.filter(r => r.hasDOMAnalysis).length > 0 ? 'status-success' : 'status-error'}"></span> تحليل DOM: ${analysisResults.filter(r => r.hasDOMAnalysis).length}/${analysisResults.length}
<span class="status-indicator ${analysisResults.filter(r => r.hasBehavioralAnalysis).length > 0 ? 'status-success' : 'status-error'}"></span> التحليل السلوكي: ${analysisResults.filter(r => r.hasBehavioralAnalysis).length}/${analysisResults.length}
<span class="status-indicator ${analysisResults.filter(r => r.hasSystemImpact).length > 0 ? 'status-success' : 'status-error'}"></span> تحليل التأثير: ${analysisResults.filter(r => r.hasSystemImpact).length}/${analysisResults.length}

🔬 تفاصيل التحليل:
${analysisResults.map(r => `
- ${r.vulnerability}: ${r.hasTextualAnalysis && r.hasDOMAnalysis && r.hasBehavioralAnalysis && r.hasSystemImpact ? '✅ تحليل شامل' : '⚠️ تحليل جزئي'}`).join('')}

${successfulAnalyses === analysisResults.length ?
  '🎉 التحليل الشامل يعمل بنجاح لجميع الثغرات!' :
  '⚠️ بعض التحليلات تحتاج تحسين'}`;

                resultDiv.className = successfulAnalyses === analysisResults.length ? 'result success' : 'result warning';

                if (successfulAnalyses === analysisResults.length) {
                    testStats.testsPassed++;
                }
                testStats.testsCompleted++;
                updateStats();

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في التحليل الشامل: ${error.message}`;
                resultDiv.className = 'result error';
                testStats.testsCompleted++;
                updateStats();
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🛡️ صفحة الاختبار النهائي الشامل جاهزة');
            updateStats();
        });
    </script>
</body>
</html>
