<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام v4 الشامل - التفاصيل والصور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .vulnerability-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 اختبار النظام v4 الشامل - التفاصيل والصور</h1>
        <p><strong>الهدف:</strong> اختبار استخدام النظام v4 للدوال الشاملة والتفاصيل الحقيقية والصور أثناء الاختبار</p>

        <div class="test-section">
            <h3>🔧 1. اختبار تهيئة النظام v4</h3>
            <button onclick="testSystemInitialization()">اختبار التهيئة</button>
            <div id="initialization-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🔬 2. اختبار TextualImpactAnalyzer</h3>
            <button onclick="testTextualImpactAnalyzer()">اختبار المحلل النصي</button>
            <div id="textual-analyzer-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📸 3. اختبار التقاط الصور أثناء الاختبار</h3>
            <button onclick="testScreenshotCaptureDuringTesting()">اختبار التقاط الصور</button>
            <div id="screenshot-capture-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🎯 4. اختبار generateRealVulnerabilityDetails</h3>
            <button onclick="testRealVulnerabilityDetails()">اختبار التفاصيل الحقيقية</button>
            <div id="real-details-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📋 5. اختبار إنشاء ثغرة شاملة</h3>
            <button onclick="testComprehensiveVulnerabilityGeneration()">اختبار إنشاء الثغرة</button>
            <div id="vuln-generation-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📊 6. اختبار التقرير الشامل</h3>
            <button onclick="testComprehensiveReport()">اختبار التقرير</button>
            <div id="report-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🔄 7. اختبار السيناريو الكامل</h3>
            <button onclick="testFullScenario()">اختبار السيناريو الكامل</button>
            <div id="full-scenario-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/textual_impact_analyzer.js"></script>
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                console.log('🔧 تهيئة نظام Bug Bounty v4...');
                bugBountyCore = new BugBountyCore();
                
                // تهيئة analysisState
                if (!bugBountyCore.analysisState) {
                    bugBountyCore.analysisState = {
                        reportId: `test_${Date.now()}`,
                        vulnerabilities: [],
                        websiteData: { url: 'https://example.com' }
                    };
                }
                
                console.log('✅ تم تهيئة النظام بنجاح');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                return false;
            }
        }

        // اختبار تهيئة النظام
        async function testSystemInitialization() {
            const resultDiv = document.getElementById('initialization-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                const initialized = await initializeSystem();
                
                // التحقق من المكونات
                const hasTextualAnalyzer = typeof window.TextualImpactAnalyzer !== 'undefined';
                const hasBugBountyCore = bugBountyCore !== null;
                const hasAnalysisState = bugBountyCore?.analysisState !== undefined;
                const hasPythonBridge = bugBountyCore?.pythonBridge !== undefined;
                
                resultDiv.innerHTML = `✅ نتيجة اختبار التهيئة:

📊 المكونات الأساسية:
<span class="status-indicator ${hasBugBountyCore ? 'status-success' : 'status-error'}"></span> BugBountyCore: ${hasBugBountyCore ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasTextualAnalyzer ? 'status-success' : 'status-error'}"></span> TextualImpactAnalyzer: ${hasTextualAnalyzer ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasAnalysisState ? 'status-success' : 'status-error'}"></span> AnalysisState: ${hasAnalysisState ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasPythonBridge ? 'status-success' : 'status-warning'}"></span> PythonBridge: ${hasPythonBridge ? 'متاح' : 'سيتم تهيئته لاحقاً'}

📋 معرف التقرير: ${bugBountyCore?.analysisState?.reportId || 'غير محدد'}

${initialized && hasBugBountyCore && hasTextualAnalyzer ? 
  '🎉 النظام v4 جاهز للاختبار!' : 
  '⚠️ النظام يحتاج إصلاح'}`;
                
                resultDiv.className = initialized && hasBugBountyCore && hasTextualAnalyzer ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التهيئة: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار TextualImpactAnalyzer
        async function testTextualImpactAnalyzer() {
            const resultDiv = document.getElementById('textual-analyzer-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // تهيئة TextualImpactAnalyzer
                await bugBountyCore.initializeTextualImpactAnalyzer();
                
                const testVuln = {
                    name: 'Test_SQL_Injection',
                    category: 'Injection',
                    severity: 'High'
                };
                
                const testExploitationResult = {
                    poc: {
                        evidence: 'MySQL error detected',
                        response_snippet: 'Database error occurred',
                        payload: "' OR '1'='1' --"
                    },
                    success: true
                };
                
                console.log('🔧 اختبار analyzeVulnerabilityDetails...');
                const analysis = await bugBountyCore.textualImpactAnalyzer.analyzeVulnerabilityDetails(
                    testVuln,
                    { url: 'https://example.com' },
                    testExploitationResult
                );
                
                const hasTextualAnalysis = analysis?.textual_analysis;
                const hasDOMAnalysis = analysis?.dom_analysis;
                const hasBehavioralAnalysis = analysis?.behavioral_analysis;
                const hasSystemImpact = analysis?.system_impact_analysis;
                
                resultDiv.innerHTML = `✅ نتيجة اختبار TextualImpactAnalyzer:

📊 أنواع التحليل المتاحة:
<span class="status-indicator ${hasTextualAnalysis ? 'status-success' : 'status-error'}"></span> التحليل النصي: ${hasTextualAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasDOMAnalysis ? 'status-success' : 'status-error'}"></span> تحليل DOM: ${hasDOMAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasBehavioralAnalysis ? 'status-success' : 'status-error'}"></span> التحليل السلوكي: ${hasBehavioralAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasSystemImpact ? 'status-success' : 'status-error'}"></span> تحليل التأثير: ${hasSystemImpact ? 'متاح' : 'غير متاح'}

📋 تفاصيل التحليل النصي:
- الوصف: ${analysis?.textual_analysis?.description || 'غير متوفر'}
- Payloads: ${analysis?.textual_analysis?.payloads_used?.length || 0}
- رسائل الخطأ: ${analysis?.textual_analysis?.error_messages_detected?.length || 0}

${hasTextualAnalysis && hasDOMAnalysis && hasBehavioralAnalysis && hasSystemImpact ? 
  '🎉 TextualImpactAnalyzer يعمل بنجاح!' : 
  '⚠️ TextualImpactAnalyzer يحتاج إصلاح'}`;
                
                resultDiv.className = hasTextualAnalysis && hasDOMAnalysis ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار TextualImpactAnalyzer: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التقاط الصور أثناء الاختبار
        async function testScreenshotCaptureDuringTesting() {
            const resultDiv = document.getElementById('screenshot-capture-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'Test_XSS_Vulnerability',
                    location: 'https://example.com',
                    visual_proof: {}
                };
                
                console.log('🔧 اختبار captureVulnerabilityScreenshotsDuringTesting...');
                await bugBountyCore.captureVulnerabilityScreenshotsDuringTesting(testVuln, 'https://example.com');
                
                const hasBeforeScreenshot = testVuln.visual_proof?.before_screenshot;
                const hasDuringScreenshot = testVuln.visual_proof?.during_screenshot;
                const hasAfterScreenshot = testVuln.visual_proof?.after_screenshot;
                const hasScreenshotsObject = testVuln.screenshots;
                const hasAnalysisStateScreenshots = bugBountyCore.analysisState?.vulnerabilityScreenshots?.[testVuln.name];
                
                resultDiv.innerHTML = `✅ نتيجة اختبار التقاط الصور:

📸 الصور في visual_proof:
<span class="status-indicator ${hasBeforeScreenshot ? 'status-success' : 'status-error'}"></span> صورة قبل: ${hasBeforeScreenshot ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${hasDuringScreenshot ? 'status-success' : 'status-error'}"></span> صورة أثناء: ${hasDuringScreenshot ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${hasAfterScreenshot ? 'status-success' : 'status-error'}"></span> صورة بعد: ${hasAfterScreenshot ? 'متاحة' : 'غير متاحة'}

📊 التخزين الإضافي:
<span class="status-indicator ${hasScreenshotsObject ? 'status-success' : 'status-error'}"></span> screenshots object: ${hasScreenshotsObject ? 'موجود' : 'غير موجود'}
<span class="status-indicator ${hasAnalysisStateScreenshots ? 'status-success' : 'status-error'}"></span> analysisState: ${hasAnalysisStateScreenshots ? 'محدث' : 'غير محدث'}

${hasBeforeScreenshot && hasDuringScreenshot && hasAfterScreenshot ? 
  '🎉 التقاط الصور أثناء الاختبار يعمل بنجاح!' : 
  '⚠️ التقاط الصور يحتاج إصلاح'}`;
                
                resultDiv.className = hasBeforeScreenshot && hasDuringScreenshot && hasAfterScreenshot ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التقاط الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار generateRealVulnerabilityDetails
        async function testRealVulnerabilityDetails() {
            const resultDiv = document.getElementById('real-details-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'Test_CSRF_Vulnerability',
                    category: 'Authentication',
                    severity: 'Medium',
                    location: 'https://example.com/form'
                };
                
                console.log('🔧 اختبار generateRealVulnerabilityDetails...');
                await bugBountyCore.generateRealVulnerabilityDetails(testVuln, null, 'https://example.com');
                
                const hasComprehensiveDetails = testVuln.comprehensive_details;
                const hasVisualProof = testVuln.visual_proof;
                const hasScreenshots = testVuln.screenshots;
                const hasPayload = testVuln.payload;
                const hasInjectionPoint = testVuln.injection_point;
                const hasExploitationResult = testVuln.exploitation_result;
                
                resultDiv.innerHTML = `✅ نتيجة اختبار التفاصيل الحقيقية:

📋 التفاصيل المولدة:
<span class="status-indicator ${hasComprehensiveDetails ? 'status-success' : 'status-error'}"></span> التفاصيل الشاملة: ${hasComprehensiveDetails ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${hasVisualProof ? 'status-success' : 'status-error'}"></span> الأدلة البصرية: ${hasVisualProof ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${hasScreenshots ? 'status-success' : 'status-error'}"></span> الصور: ${hasScreenshots ? 'متاحة' : 'غير متاحة'}

📊 البيانات التقنية:
<span class="status-indicator ${hasPayload ? 'status-success' : 'status-warning'}"></span> Payload: ${hasPayload ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasInjectionPoint ? 'status-success' : 'status-warning'}"></span> نقطة الحقن: ${hasInjectionPoint ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${hasExploitationResult ? 'status-success' : 'status-warning'}"></span> نتيجة الاستغلال: ${hasExploitationResult ? 'متاحة' : 'غير متاحة'}

📋 طول التفاصيل الشاملة: ${hasComprehensiveDetails ? testVuln.comprehensive_details.length : 0} حرف

${hasComprehensiveDetails && hasVisualProof ? 
  '🎉 إنشاء التفاصيل الحقيقية يعمل بنجاح!' : 
  '⚠️ إنشاء التفاصيل الحقيقية يحتاج إصلاح'}`;
                
                resultDiv.className = hasComprehensiveDetails && hasVisualProof ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التفاصيل الحقيقية: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار إنشاء ثغرة شاملة
        async function testComprehensiveVulnerabilityGeneration() {
            const resultDiv = document.getElementById('vuln-generation-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await initializeSystem();

                // إنشاء ثغرة جديدة
                const testVuln = {
                    name: 'Comprehensive_Test_Vulnerability',
                    category: 'Injection',
                    severity: 'Critical',
                    location: 'https://example.com/test'
                };

                console.log('🔧 اختبار إنشاء ثغرة شاملة...');

                // تطبيق جميع التحسينات
                await bugBountyCore.generateRealVulnerabilityDetails(testVuln, null, 'https://example.com');

                // التحقق من التحليل الشامل
                let comprehensiveAnalysis = null;
                if (bugBountyCore.textualImpactAnalyzer) {
                    comprehensiveAnalysis = await bugBountyCore.textualImpactAnalyzer.analyzeVulnerabilityDetails(
                        testVuln,
                        { url: 'https://example.com' },
                        testVuln.exploitation_result || {}
                    );
                }

                const hasAllComponents = testVuln.comprehensive_details &&
                                       testVuln.visual_proof &&
                                       testVuln.screenshots &&
                                       comprehensiveAnalysis;

                const hasTextualAnalysis = comprehensiveAnalysis?.textual_analysis;
                const hasDOMAnalysis = comprehensiveAnalysis?.dom_analysis;
                const hasBehavioralAnalysis = comprehensiveAnalysis?.behavioral_analysis;
                const hasSystemImpact = comprehensiveAnalysis?.system_impact_analysis;

                resultDiv.innerHTML = `✅ نتيجة اختبار إنشاء الثغرة الشاملة:

📊 مكونات الثغرة:
<span class="status-indicator ${testVuln.comprehensive_details ? 'status-success' : 'status-error'}"></span> التفاصيل الشاملة: ${testVuln.comprehensive_details ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${testVuln.visual_proof ? 'status-success' : 'status-error'}"></span> الأدلة البصرية: ${testVuln.visual_proof ? 'متاحة' : 'غير متاحة'}
<span class="status-indicator ${testVuln.screenshots ? 'status-success' : 'status-error'}"></span> الصور: ${testVuln.screenshots ? 'متاحة' : 'غير متاحة'}

📋 التحليل الشامل:
<span class="status-indicator ${hasTextualAnalysis ? 'status-success' : 'status-error'}"></span> التحليل النصي: ${hasTextualAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasDOMAnalysis ? 'status-success' : 'status-error'}"></span> تحليل DOM: ${hasDOMAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasBehavioralAnalysis ? 'status-success' : 'status-error'}"></span> التحليل السلوكي: ${hasBehavioralAnalysis ? 'متاح' : 'غير متاح'}
<span class="status-indicator ${hasSystemImpact ? 'status-success' : 'status-error'}"></span> تحليل التأثير: ${hasSystemImpact ? 'متاح' : 'غير متاح'}

📋 معلومات إضافية:
- اسم الثغرة: ${testVuln.name}
- الفئة: ${testVuln.category}
- الخطورة: ${testVuln.severity}
- الموقع: ${testVuln.location}

${hasAllComponents ?
  '🎉 إنشاء الثغرة الشاملة يعمل بنجاح!' :
  '⚠️ إنشاء الثغرة الشاملة يحتاج إصلاح'}`;

                resultDiv.className = hasAllComponents ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار إنشاء الثغرة الشاملة: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التقرير الشامل
        async function testComprehensiveReport() {
            const resultDiv = document.getElementById('report-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await initializeSystem();

                // إنشاء ثغرات اختبار
                const testVulns = [
                    {
                        name: 'Report_Test_SQL_Injection',
                        category: 'Injection',
                        severity: 'High',
                        location: 'https://example.com/login'
                    },
                    {
                        name: 'Report_Test_XSS',
                        category: 'Injection',
                        severity: 'Medium',
                        location: 'https://example.com/search'
                    }
                ];

                // تطبيق التحسينات على كل ثغرة
                for (const vuln of testVulns) {
                    await bugBountyCore.generateRealVulnerabilityDetails(vuln, null, vuln.location);
                }

                // إضافة الثغرات إلى analysisState
                bugBountyCore.analysisState.vulnerabilities = testVulns;

                console.log('🔧 اختبار generateVulnerabilitiesHTML...');
                const vulnerabilitiesHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulns);

                // التحقق من محتوى التقرير
                const hasComprehensiveDetails = vulnerabilitiesHTML.includes('التحليل النصي المتقدم') ||
                                              vulnerabilitiesHTML.includes('تحليل تغيرات DOM') ||
                                              vulnerabilitiesHTML.includes('التحليل السلوكي');

                const hasScreenshots = vulnerabilitiesHTML.includes('data:image/png;base64,') ||
                                     vulnerabilitiesHTML.includes('📸 الصور الحقيقية');

                const hasRealDetails = !vulnerabilitiesHTML.includes('SQL Injection SQL Injection') &&
                                     !vulnerabilitiesHTML.includes('XSS XSS XSS');

                const reportLength = vulnerabilitiesHTML.length;

                resultDiv.innerHTML = `✅ نتيجة اختبار التقرير الشامل:

📊 محتوى التقرير:
<span class="status-indicator ${hasComprehensiveDetails ? 'status-success' : 'status-error'}"></span> التفاصيل الشاملة: ${hasComprehensiveDetails ? 'موجودة' : 'غير موجودة'}
<span class="status-indicator ${hasScreenshots ? 'status-success' : 'status-error'}"></span> الصور: ${hasScreenshots ? 'موجودة' : 'غير موجودة'}
<span class="status-indicator ${hasRealDetails ? 'status-success' : 'status-error'}"></span> تفاصيل حقيقية: ${hasRealDetails ? 'موجودة' : 'نص مكرر'}

📋 معلومات التقرير:
- طول التقرير: ${reportLength} حرف
- عدد الثغرات: ${testVulns.length}
- يحتوي على HTML: ${vulnerabilitiesHTML.includes('<div') ? 'نعم' : 'لا'}

<div class="vulnerability-preview">
<h4>معاينة جزء من التقرير:</h4>
${vulnerabilitiesHTML.substring(0, 500)}...
</div>

${hasComprehensiveDetails && hasRealDetails ?
  '🎉 التقرير الشامل يعمل بنجاح!' :
  '⚠️ التقرير الشامل يحتاج إصلاح'}`;

                resultDiv.className = hasComprehensiveDetails && hasRealDetails ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التقرير الشامل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار السيناريو الكامل
        async function testFullScenario() {
            const resultDiv = document.getElementById('full-scenario-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';

            try {
                if (!bugBountyCore) await initializeSystem();

                console.log('🔧 بدء السيناريو الكامل...');

                // 1. إنشاء ثغرة جديدة
                const testVuln = {
                    name: 'Full_Scenario_Test_Vulnerability',
                    category: 'Injection',
                    severity: 'Critical',
                    location: 'https://example.com/full-test'
                };

                // 2. تطبيق جميع التحسينات
                await bugBountyCore.generateRealVulnerabilityDetails(testVuln, null, testVuln.location);

                // 3. التحليل الشامل
                let comprehensiveAnalysis = null;
                if (bugBountyCore.textualImpactAnalyzer) {
                    comprehensiveAnalysis = await bugBountyCore.textualImpactAnalyzer.analyzeVulnerabilityDetails(
                        testVuln,
                        { url: testVuln.location },
                        testVuln.exploitation_result || {}
                    );
                }

                // 4. إنشاء تقرير
                bugBountyCore.analysisState.vulnerabilities = [testVuln];
                const reportHTML = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);

                // 5. التحقق من النتائج
                const hasAllComponents = testVuln.comprehensive_details &&
                                       testVuln.visual_proof &&
                                       comprehensiveAnalysis &&
                                       reportHTML.length > 1000;

                const hasScreenshotsInVuln = testVuln.visual_proof?.before_screenshot &&
                                           testVuln.visual_proof?.during_screenshot &&
                                           testVuln.visual_proof?.after_screenshot;

                const hasComprehensiveReport = reportHTML.includes('التحليل النصي المتقدم') ||
                                             reportHTML.includes('تحليل تغيرات DOM') ||
                                             reportHTML.includes('التحليل السلوكي');

                resultDiv.innerHTML = `✅ نتيجة السيناريو الكامل:

📊 المراحل المكتملة:
<span class="status-indicator status-success"></span> إنشاء الثغرة: نجح
<span class="status-indicator ${testVuln.comprehensive_details ? 'status-success' : 'status-error'}"></span> التفاصيل الحقيقية: ${testVuln.comprehensive_details ? 'نجح' : 'فشل'}
<span class="status-indicator ${hasScreenshotsInVuln ? 'status-success' : 'status-error'}"></span> التقاط الصور: ${hasScreenshotsInVuln ? 'نجح' : 'فشل'}
<span class="status-indicator ${comprehensiveAnalysis ? 'status-success' : 'status-error'}"></span> التحليل الشامل: ${comprehensiveAnalysis ? 'نجح' : 'فشل'}
<span class="status-indicator ${hasComprehensiveReport ? 'status-success' : 'status-error'}"></span> التقرير الشامل: ${hasComprehensiveReport ? 'نجح' : 'فشل'}

📋 النتائج النهائية:
- اسم الثغرة: ${testVuln.name}
- طول التفاصيل: ${testVuln.comprehensive_details?.length || 0} حرف
- عدد الصور: ${hasScreenshotsInVuln ? 3 : 0}
- طول التقرير: ${reportHTML.length} حرف

📊 أنواع التحليل:
- التحليل النصي: ${comprehensiveAnalysis?.textual_analysis ? 'متاح' : 'غير متاح'}
- تحليل DOM: ${comprehensiveAnalysis?.dom_analysis ? 'متاح' : 'غير متاح'}
- التحليل السلوكي: ${comprehensiveAnalysis?.behavioral_analysis ? 'متاح' : 'غير متاح'}
- تحليل التأثير: ${comprehensiveAnalysis?.system_impact_analysis ? 'متاح' : 'غير متاح'}

${hasAllComponents && hasScreenshotsInVuln && hasComprehensiveReport ?
  '🎉 السيناريو الكامل يعمل بنجاح! النظام v4 جاهز للاستخدام.' :
  '⚠️ السيناريو الكامل يحتاج إصلاح - راجع المراحل الفاشلة أعلاه.'}`;

                resultDiv.className = hasAllComponents && hasScreenshotsInVuln && hasComprehensiveReport ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في السيناريو الكامل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
