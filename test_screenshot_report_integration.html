<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط الصور مع التقارير - v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .screenshot-preview {
            max-width: 150px;
            max-height: 100px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px;
        }
        .screenshot-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        .screenshot-item {
            text-align: center;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار ربط الصور مع التقارير - v4.0</h1>
        <p><strong>الهدف:</strong> اختبار ربط الصور المحفوظة في المجلدات مع عرضها في التقارير HTML</p>

        <div class="test-section">
            <h3>🔧 1. اختبار endpoint قراءة الصور</h3>
            <button onclick="testGetReportScreenshots()">اختبار قراءة الصور</button>
            <div id="get-screenshots-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📂 2. اختبار loadSavedScreenshotsForReport</h3>
            <button onclick="testLoadSavedScreenshots()">اختبار تحميل الصور</button>
            <div id="load-screenshots-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🔗 3. اختبار linkScreenshotToVulnerability</h3>
            <button onclick="testLinkScreenshotToVulnerability()">اختبار ربط الصور</button>
            <div id="link-screenshots-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📊 4. اختبار التكامل مع التقارير</h3>
            <button onclick="testReportIntegration()">اختبار التكامل</button>
            <div id="report-integration-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🎯 5. اختبار سيناريو كامل</h3>
            <button onclick="testFullScenario()">اختبار السيناريو الكامل</button>
            <div id="full-scenario-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📸 6. عرض الصور المحفوظة</h3>
            <button onclick="displaySavedScreenshots()">عرض الصور</button>
            <div id="display-screenshots-result" class="result info">انقر على الزر لعرض الصور...</div>
            <div id="screenshots-container" class="screenshot-container"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testReportId = `test_report_${Date.now()}`;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                console.log('🔧 تهيئة نظام Bug Bounty v4...');
                bugBountyCore = new BugBountyCore();
                
                // تعيين reportId للاختبار
                if (!bugBountyCore.analysisState) {
                    bugBountyCore.analysisState = {};
                }
                bugBountyCore.analysisState.reportId = testReportId;
                
                console.log(`✅ تم تهيئة النظام بنجاح - Report ID: ${testReportId}`);
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                return false;
            }
        }

        // اختبار endpoint قراءة الصور
        async function testGetReportScreenshots() {
            const resultDiv = document.getElementById('get-screenshots-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                // أولاً إنشاء بعض الصور للاختبار
                console.log('🔧 إنشاء صور اختبار...');
                await createTestScreenshots();
                
                // اختبار endpoint
                const response = await fetch('http://localhost:8000/get_report_screenshots', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_id: testReportId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                resultDiv.innerHTML = `✅ نتيجة اختبار endpoint:

📊 حالة النجاح: ${result.success ? 'نجح' : 'فشل'}
📸 عدد الصور: ${result.total_screenshots || 0}
📋 معرف التقرير: ${result.report_id}
📂 مجلد التقرير: ${result.report_dir || 'غير محدد'}

📸 الصور المكتشفة:
${result.screenshots?.map(img => 
  `<span class="status-indicator status-success"></span> ${img.filename} (${Math.round(img.file_size / 1024)}KB)`
).join('\n') || 'لا توجد صور'}

${result.success && result.total_screenshots > 0 ? 
  '🎉 endpoint يعمل بنجاح!' : 
  '⚠️ endpoint يحتاج إصلاح أو لا توجد صور'}`;
                
                resultDiv.className = result.success && result.total_screenshots > 0 ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار endpoint: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // إنشاء صور اختبار
        async function createTestScreenshots() {
            try {
                // إنشاء صورة اختبار بسيطة (1x1 pixel PNG)
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                // رسم مربعات ملونة للاختبار
                const colors = ['#ff0000', '#00ff00', '#0000ff'];
                const stages = ['before', 'during', 'after'];
                
                for (let i = 0; i < 3; i++) {
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(0, 0, 100, 100);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(stages[i], 10, 50);
                    
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    
                    // حفظ الصورة باستخدام Python Service
                    await fetch('http://localhost:8000/save_screenshot', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            screenshot_data: imageData,
                            filename: `test_vulnerability_${stages[i]}`,
                            report_id: testReportId
                        })
                    });
                }
                
                console.log('✅ تم إنشاء صور الاختبار');
            } catch (error) {
                console.error('❌ خطأ في إنشاء صور الاختبار:', error);
            }
        }

        // اختبار تحميل الصور المحفوظة
        async function testLoadSavedScreenshots() {
            const resultDiv = document.getElementById('load-screenshots-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                console.log('🔧 اختبار loadSavedScreenshotsForReport...');
                const screenshots = await bugBountyCore.loadSavedScreenshotsForReport(testReportId);
                
                const hasScreenshots = screenshots && screenshots.length > 0;
                
                resultDiv.innerHTML = `✅ نتيجة اختبار تحميل الصور:

📊 حالة النجاح: ${hasScreenshots ? 'نجح' : 'فشل'}
📸 عدد الصور المحملة: ${screenshots?.length || 0}
📋 معرف التقرير: ${testReportId}

📸 الصور المحملة:
${screenshots?.map(img => 
  `<span class="status-indicator status-success"></span> ${img.filename} (${Math.round(img.file_size / 1024)}KB)`
).join('\n') || 'لا توجد صور'}

📊 حالة analysisState.savedScreenshots: ${bugBountyCore.analysisState.savedScreenshots?.[testReportId] ? 'محدث' : 'غير محدث'}

${hasScreenshots ? 
  '🎉 تحميل الصور يعمل بنجاح!' : 
  '⚠️ تحميل الصور يحتاج إصلاح'}`;
                
                resultDiv.className = hasScreenshots ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار تحميل الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار ربط الصور مع الثغرات
        async function testLinkScreenshotToVulnerability() {
            const resultDiv = document.getElementById('link-screenshots-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // إنشاء ثغرة اختبار
                const testVuln = {
                    name: 'Test_Vulnerability',
                    type: 'SQL Injection',
                    severity: 'High',
                    location: 'https://example.com/test'
                };
                
                // إضافة الثغرة إلى analysisState
                if (!bugBountyCore.analysisState.vulnerabilities) {
                    bugBountyCore.analysisState.vulnerabilities = [];
                }
                bugBountyCore.analysisState.vulnerabilities.push(testVuln);
                
                // اختبار ربط الصور
                const testScreenshotData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
                
                console.log('🔧 اختبار linkScreenshotToVulnerability...');
                bugBountyCore.linkScreenshotToVulnerability('test_vulnerability_before', testScreenshotData, testReportId);
                bugBountyCore.linkScreenshotToVulnerability('test_vulnerability_during', testScreenshotData, testReportId);
                bugBountyCore.linkScreenshotToVulnerability('test_vulnerability_after', testScreenshotData, testReportId);
                
                // التحقق من النتائج
                const linkedVuln = bugBountyCore.analysisState.vulnerabilities.find(v => v.name === 'Test_Vulnerability');
                const hasBeforeScreenshot = linkedVuln?.visual_proof?.before_screenshot;
                const hasDuringScreenshot = linkedVuln?.visual_proof?.during_screenshot;
                const hasAfterScreenshot = linkedVuln?.visual_proof?.after_screenshot;
                
                resultDiv.innerHTML = `✅ نتيجة اختبار ربط الصور:

📊 حالة الربط: ${linkedVuln ? 'نجح' : 'فشل'}
🎯 اسم الثغرة: ${linkedVuln?.name || 'غير موجودة'}

📸 الصور المربوطة:
<span class="status-indicator ${hasBeforeScreenshot ? 'status-success' : 'status-error'}"></span> صورة قبل: ${hasBeforeScreenshot ? 'مربوطة' : 'غير مربوطة'}
<span class="status-indicator ${hasDuringScreenshot ? 'status-success' : 'status-error'}"></span> صورة أثناء: ${hasDuringScreenshot ? 'مربوطة' : 'غير مربوطة'}
<span class="status-indicator ${hasAfterScreenshot ? 'status-success' : 'status-error'}"></span> صورة بعد: ${hasAfterScreenshot ? 'مربوطة' : 'غير مربوطة'}

📊 visual_proof موجود: ${linkedVuln?.visual_proof ? 'نعم' : 'لا'}
📊 screenshots موجود: ${linkedVuln?.screenshots ? 'نعم' : 'لا'}

${hasBeforeScreenshot && hasDuringScreenshot && hasAfterScreenshot ? 
  '🎉 ربط الصور يعمل بنجاح!' : 
  '⚠️ ربط الصور يحتاج إصلاح'}`;
                
                resultDiv.className = hasBeforeScreenshot && hasDuringScreenshot && hasAfterScreenshot ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار ربط الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التكامل مع التقارير
        async function testReportIntegration() {
            const resultDiv = document.getElementById('report-integration-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // تحميل الصور المحفوظة
                await bugBountyCore.loadSavedScreenshotsForReport(testReportId);
                
                // اختبار إنشاء تقرير مع الصور
                const testReport = {
                    vulnerabilities: bugBountyCore.analysisState.vulnerabilities || []
                };
                
                console.log('🔧 اختبار enrichPageReportWithComprehensiveData...');
                const enrichedReport = await bugBountyCore.enrichPageReportWithComprehensiveData(testReport, 1);
                
                // التحقق من وجود الصور في التقرير المحسن
                const hasScreenshotsInReport = enrichedReport.vulnerabilities?.some(vuln => 
                    vuln.visual_proof?.before_screenshot || 
                    vuln.visual_proof?.during_screenshot || 
                    vuln.visual_proof?.after_screenshot
                );
                
                resultDiv.innerHTML = `✅ نتيجة اختبار التكامل مع التقارير:

📊 حالة التحسين: ${enrichedReport ? 'نجح' : 'فشل'}
📸 الصور في التقرير: ${hasScreenshotsInReport ? 'موجودة' : 'غير موجودة'}
🎯 عدد الثغرات: ${enrichedReport.vulnerabilities?.length || 0}

📊 تفاصيل التقرير المحسن:
- الثغرات: ${enrichedReport.vulnerabilities?.length || 0}
- الحوارات: ${enrichedReport.dialogues?.length || 0}
- الصور: ${enrichedReport.screenshots?.length || 0}
- النتائج: ${enrichedReport.test_results?.length || 0}

${enrichedReport && hasScreenshotsInReport ? 
  '🎉 التكامل مع التقارير يعمل بنجاح!' : 
  '⚠️ التكامل مع التقارير يحتاج إصلاح'}`;
                
                resultDiv.className = enrichedReport && hasScreenshotsInReport ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار التكامل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار السيناريو الكامل
        async function testFullScenario() {
            const resultDiv = document.getElementById('full-scenario-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                console.log('🔧 بدء السيناريو الكامل...');
                
                // 1. إنشاء صور اختبار
                await createTestScreenshots();
                
                // 2. تحميل الصور
                const screenshots = await bugBountyCore.loadSavedScreenshotsForReport(testReportId);
                
                // 3. إنشاء ثغرة وربط الصور
                const testVuln = {
                    name: 'Full_Test_Vulnerability',
                    type: 'XSS',
                    severity: 'Critical'
                };
                
                if (!bugBountyCore.analysisState.vulnerabilities) {
                    bugBountyCore.analysisState.vulnerabilities = [];
                }
                bugBountyCore.analysisState.vulnerabilities.push(testVuln);
                
                // 4. ربط الصور
                if (screenshots && screenshots.length > 0) {
                    for (const screenshot of screenshots) {
                        bugBountyCore.linkScreenshotToVulnerability(
                            screenshot.filename, 
                            screenshot.screenshot_data, 
                            testReportId
                        );
                    }
                }
                
                // 5. إنشاء تقرير نهائي
                const finalReport = await bugBountyCore.generateFinalComprehensiveReport(
                    bugBountyCore.analysisState,
                    [],
                    'https://example.com'
                );
                
                // التحقق من النتائج
                const hasScreenshotsInFinalReport = finalReport && finalReport.includes('data:image/png;base64,');
                const reportLength = finalReport ? finalReport.length : 0;
                
                resultDiv.innerHTML = `✅ نتيجة السيناريو الكامل:

📊 المراحل المكتملة:
<span class="status-indicator status-success"></span> إنشاء صور الاختبار: نجح
<span class="status-indicator ${screenshots?.length > 0 ? 'status-success' : 'status-error'}"></span> تحميل الصور: ${screenshots?.length > 0 ? 'نجح' : 'فشل'}
<span class="status-indicator status-success"></span> إنشاء الثغرات: نجح
<span class="status-indicator status-success"></span> ربط الصور: نجح
<span class="status-indicator ${finalReport ? 'status-success' : 'status-error'}"></span> إنشاء التقرير النهائي: ${finalReport ? 'نجح' : 'فشل'}

📊 نتائج التقرير النهائي:
- طول التقرير: ${reportLength} حرف
- يحتوي على صور: ${hasScreenshotsInFinalReport ? 'نعم' : 'لا'}
- عدد الثغرات: ${bugBountyCore.analysisState.vulnerabilities?.length || 0}

${finalReport && hasScreenshotsInFinalReport ? 
  '🎉 السيناريو الكامل يعمل بنجاح!' : 
  '⚠️ السيناريو الكامل يحتاج إصلاح'}`;
                
                resultDiv.className = finalReport && hasScreenshotsInFinalReport ? 'result success' : 'result warning';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في السيناريو الكامل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // عرض الصور المحفوظة
        async function displaySavedScreenshots() {
            const resultDiv = document.getElementById('display-screenshots-result');
            const containerDiv = document.getElementById('screenshots-container');
            
            resultDiv.textContent = 'جاري تحميل الصور...';
            resultDiv.className = 'result info';
            containerDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://localhost:8000/get_report_screenshots', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_id: testReportId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success && result.screenshots && result.screenshots.length > 0) {
                    resultDiv.innerHTML = `✅ تم تحميل ${result.screenshots.length} صورة بنجاح`;
                    resultDiv.className = 'result success';
                    
                    // عرض الصور
                    for (const screenshot of result.screenshots) {
                        const screenshotItem = document.createElement('div');
                        screenshotItem.className = 'screenshot-item';
                        
                        const img = document.createElement('img');
                        img.src = `data:image/png;base64,${screenshot.screenshot_data}`;
                        img.className = 'screenshot-preview';
                        img.alt = screenshot.filename;
                        
                        const label = document.createElement('div');
                        label.textContent = screenshot.filename;
                        
                        screenshotItem.appendChild(img);
                        screenshotItem.appendChild(label);
                        containerDiv.appendChild(screenshotItem);
                    }
                } else {
                    resultDiv.textContent = 'لا توجد صور محفوظة للعرض';
                    resultDiv.className = 'result warning';
                }
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في عرض الصور: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
