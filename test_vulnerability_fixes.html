<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات الثغرات - v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاحات نظام Bug Bounty v4.0</h1>
        
        <div class="test-section">
            <h3>1. اختبار إنشاء تفاصيل الثغرات الحقيقية</h3>
            <button class="test-button" onclick="testVulnerabilityDetails()">اختبار التفاصيل الشاملة</button>
            <div id="details-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار نوع الاستغلال</h3>
            <button class="test-button" onclick="testExploitationType()">اختبار نوع الاستغلال</button>
            <div id="exploitation-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار إنشاء Payloads حقيقية</h3>
            <button class="test-button" onclick="testPayloadGeneration()">اختبار Payloads</button>
            <div id="payload-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار خدمة الصور Python</h3>
            <button class="test-button" onclick="testPythonService()">اختبار خدمة Python</button>
            <div id="python-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار إضافة ثغرة كاملة</h3>
            <button class="test-button" onclick="testAddVulnerability()">اختبار إضافة ثغرة</button>
            <div id="vuln-result" class="result"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                bugBountyCore = new BugBountyCore();
                await bugBountyCore.initialize();
                console.log('✅ تم تهيئة نظام Bug Bounty v4.0 بنجاح');
            } catch (error) {
                console.error('❌ فشل في تهيئة النظام:', error);
            }
        }

        // اختبار إنشاء تفاصيل الثغرات
        async function testVulnerabilityDetails() {
            const resultDiv = document.getElementById('details-result');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'SQL Injection',
                    severity: 'High',
                    target: 'https://example.com/login'
                };

                const payload = bugBountyCore.generateRealPayloadForVulnerability(testVuln);
                const exploitResult = bugBountyCore.generateRealExploitationResult(testVuln);
                const errorMessages = bugBountyCore.generateRealErrorMessages(testVuln);
                const exploitationType = bugBountyCore.getExploitationType(testVuln.name);

                const result = `✅ نتائج الاختبار:

🔥 Payload المولد: ${payload}

✅ نتيجة الاستغلال: ${exploitResult}

⚠️ رسائل الخطأ: ${errorMessages.join(', ')}

💥 نوع الاستغلال: ${exploitationType}`;

                resultDiv.textContent = result;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار نوع الاستغلال
        async function testExploitationType() {
            const resultDiv = document.getElementById('exploitation-result');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const vulnTypes = ['SQL Injection', 'XSS', 'NoSQL Injection', 'Template Injection'];
                let results = '✅ نتائج اختبار أنواع الاستغلال:\n\n';
                
                vulnTypes.forEach(vulnType => {
                    const exploitationType = bugBountyCore.getExploitationType(vulnType);
                    results += `${vulnType}: ${exploitationType}\n`;
                });

                resultDiv.textContent = results;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار إنشاء Payloads
        async function testPayloadGeneration() {
            const resultDiv = document.getElementById('payload-result');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const vulnTypes = ['SQL Injection', 'XSS', 'NoSQL Injection', 'Template Injection'];
                let results = '✅ نتائج اختبار Payloads:\n\n';
                
                vulnTypes.forEach(vulnType => {
                    const payload = bugBountyCore.generateRealPayloadForVulnerability(vulnType);
                    results += `${vulnType}:\n${payload}\n\n`;
                });

                resultDiv.textContent = results;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار خدمة Python
        async function testPythonService() {
            const resultDiv = document.getElementById('python-result');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `✅ خدمة Python تعمل بنجاح:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.textContent = `❌ خدمة Python لا تعمل: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار إضافة ثغرة كاملة
        async function testAddVulnerability() {
            const resultDiv = document.getElementById('vuln-result');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'SQL Injection',
                    severity: 'High',
                    target: 'https://example.com/login',
                    description: 'ثغرة حقن SQL مكتشفة في نموذج تسجيل الدخول'
                };

                // محاكاة إضافة الثغرة
                await bugBountyCore.addVulnerability(testVuln);
                
                const vulnCount = bugBountyCore.currentScan?.vulnerabilities?.length || 0;
                resultDiv.textContent = `✅ تم إضافة الثغرة بنجاح!\nعدد الثغرات المكتشفة: ${vulnCount}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في إضافة الثغرة: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
