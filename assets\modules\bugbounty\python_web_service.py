#!/usr/bin/env python3
"""
خدمة ويب Python لالتقاط الصور الحقيقية للمواقع
تستخدم Selenium و Playwright لالتقاط صور عالية الجودة
"""

import os
import sys
import json
import base64
import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Flask للخدمة الويب
try:
    from flask import Flask, request, jsonify, send_file
    from flask_cors import CORS
except ImportError:
    print("❌ Flask غير مثبت. قم بتثبيته: pip install flask flask-cors")
    sys.exit(1)

# Selenium للتقاط الصور
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service as ChromeService
except ImportError:
    print("❌ Selenium غير مثبت. قم بتثبيته: pip install selenium")
    sys.exit(1)

# WebDriver Manager لتحميل ChromeDriver تلقائياً
try:
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    print("❌ WebDriver Manager غير مثبت. قم بتثبيته: pip install webdriver-manager")
    sys.exit(1)

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)  # السماح بطلبات CORS

class ScreenshotService:
    """خدمة التقاط الصور الحقيقية"""
    
    def __init__(self):
        self.screenshots_dir = Path("./screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        self.stats = {
            "total_requests": 0,
            "successful_captures": 0,
            "failed_captures": 0,
            "start_time": datetime.now().isoformat()
        }
        
        # إعداد Chrome options
        self.chrome_options = ChromeOptions()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        self.chrome_options.add_argument('--disable-extensions')
        self.chrome_options.add_argument('--disable-plugins')
        self.chrome_options.add_argument('--disable-images')  # تسريع التحميل
        
        logger.info("🐍 تم تهيئة خدمة التقاط الصور Python")
    
    def capture_screenshot(self, url: str, report_id: str, width: int = 1920, height: int = 1080, wait_time: int = 3) -> Dict[str, Any]:
        """التقاط صورة حقيقية للموقع"""
        
        self.stats["total_requests"] += 1
        driver = None
        
        try:
            logger.info(f"📸 بدء التقاط صورة للموقع: {url}")
            
            # إنشاء مجلد للتقرير
            report_dir = self.screenshots_dir / report_id
            report_dir.mkdir(exist_ok=True)
            
            # إنشاء driver مع تحميل ChromeDriver تلقائياً
            chrome_service = ChromeService(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=chrome_service, options=self.chrome_options)
            driver.set_window_size(width, height)
            
            # تحميل الصفحة
            driver.get(url)
            
            # انتظار تحميل الصفحة
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # انتظار إضافي
            time.sleep(wait_time)
            
            # التقاط الصورة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{report_id}_{timestamp}.png"
            file_path = report_dir / filename
            
            # حفظ الصورة
            driver.save_screenshot(str(file_path))
            
            # قراءة الصورة وتحويلها إلى base64
            with open(file_path, 'rb') as img_file:
                img_data = img_file.read()
                base64_data = base64.b64encode(img_data).decode('utf-8')
            
            # الحصول على أبعاد الصورة الفعلية
            actual_size = driver.get_window_size()
            
            self.stats["successful_captures"] += 1
            
            result = {
                "success": True,
                "base64_data": base64_data,
                "screenshot_data": base64_data,  # إضافة screenshot_data للتوافق
                "file_path": str(file_path),
                "width": actual_size.get("width", width),
                "height": actual_size.get("height", height),
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "report_id": report_id,
                "file_size": len(img_data),
                "method": "selenium_chrome"
            }
            
            logger.info(f"✅ تم التقاط الصورة بنجاح: {filename}")
            return result
            
        except Exception as e:
            self.stats["failed_captures"] += 1
            logger.error(f"❌ فشل في التقاط الصورة: {str(e)}")
            
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "report_id": report_id
            }
            
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

# إنشاء خدمة التقاط الصور
screenshot_service = ScreenshotService()

@app.route('/health', methods=['GET'])
def health_check():
    """فحص صحة الخدمة"""
    return jsonify({
        "status": "healthy",
        "service": "Python Screenshot Service",
        "version": "4.0",
        "timestamp": datetime.now().isoformat(),
        "stats": screenshot_service.stats
    })

@app.route('/capture', methods=['POST'])
def capture_screenshot():
    """التقاط صورة للموقع"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({
                "success": False,
                "error": "URL مطلوب"
            }), 400
        
        url = data['url']
        report_id = data.get('report_id', f'screenshot_{int(datetime.now().timestamp())}')
        width = data.get('width', 1920)
        height = data.get('height', 1080)
        wait_time = data.get('wait_time', 3)
        
        # التقاط الصورة
        result = screenshot_service.capture_screenshot(url, report_id, width, height, wait_time)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"❌ خطأ في معالجة الطلب: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/vulnerability_sequence', methods=['POST'])
def capture_vulnerability_sequence():
    """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد)"""
    try:
        data = request.get_json()

        if not data or 'url' not in data or 'vulnerability_name' not in data or 'report_id' not in data:
            return jsonify({
                "success": False,
                "error": "URL واسم الثغرة ومعرف التقرير مطلوبة"
            }), 400

        url = data['url']
        vulnerability_name = data['vulnerability_name']
        report_id = data['report_id']

        # استخدام screenshot_service لالتقاط تسلسل الصور
        import asyncio
        from screenshot_service import capture_vulnerability_screenshots_v4

        # تشغيل الدالة async
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(capture_vulnerability_screenshots_v4(url, vulnerability_name, report_id))
        loop.close()

        if result:
            return jsonify({
                "success": True,
                "before": result.get('before'),
                "during": result.get('during'),
                "after": result.get('after'),
                "report_id": report_id,
                "vulnerability_name": vulnerability_name
            })
        else:
            return jsonify({
                "success": False,
                "error": "فشل في التقاط تسلسل صور الثغرة"
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط تسلسل صور الثغرة: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/before_after', methods=['POST'])
def capture_before_after():
    """التقاط صور قبل وبعد للثغرة"""
    try:
        data = request.get_json()

        if not data or 'url' not in data or 'report_id' not in data:
            return jsonify({
                "success": False,
                "error": "URL ومعرف التقرير مطلوبان"
            }), 400

        url = data['url']
        report_id = data['report_id']
        vulnerability_name = data.get('vulnerability_name')

        # استخدام screenshot_service لالتقاط صور قبل وبعد
        import asyncio
        from screenshot_service import capture_before_after_screenshots_v4

        # تشغيل الدالة async
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(capture_before_after_screenshots_v4(url, report_id, vulnerability_name))
        loop.close()

        if result and result.get('success'):
            return jsonify({
                "success": True,
                "before": result.get('before'),
                "after": result.get('after'),
                "report_id": report_id
            })
        else:
            return jsonify({
                "success": False,
                "error": "فشل في التقاط صور قبل وبعد"
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صور قبل وبعد: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """الحصول على إحصائيات الخدمة"""
    return jsonify(screenshot_service.stats)

@app.route('/save_screenshot', methods=['POST'])
def save_screenshot():
    """حفظ الصورة في مجلد منفصل"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "لا توجد بيانات"}), 400

        screenshot_data = data.get('screenshot_data')
        filename = data.get('filename')
        report_id = data.get('report_id')

        if not all([screenshot_data, filename, report_id]):
            return jsonify({"error": "بيانات مفقودة: screenshot_data, filename, report_id"}), 400

        # إنشاء مجلد التقرير
        report_dir = Path("screenshots") / report_id
        report_dir.mkdir(parents=True, exist_ok=True)

        # تحديد مسار الملف
        file_path = report_dir / f"{filename}.png"

        # تحويل Base64 إلى صورة
        try:
            # إزالة البادئة إذا كانت موجودة
            if screenshot_data.startswith('data:image'):
                screenshot_data = screenshot_data.split(',')[1]

            # فك تشفير Base64
            image_data = base64.b64decode(screenshot_data)

            # حفظ الصورة
            with open(file_path, 'wb') as f:
                f.write(image_data)

            logger.info(f"✅ تم حفظ الصورة: {file_path}")

            return jsonify({
                "success": True,
                "file_path": str(file_path),
                "message": f"تم حفظ الصورة بنجاح في {file_path}"
            })

        except Exception as decode_error:
            logger.error(f"❌ خطأ في فك تشفير الصورة: {decode_error}")
            return jsonify({"error": f"خطأ في فك تشفير الصورة: {str(decode_error)}"}), 400

    except Exception as e:
        logger.error(f"❌ خطأ في حفظ الصورة: {e}")
        return jsonify({"error": f"خطأ في حفظ الصورة: {str(e)}"}), 500

@app.route('/v4_website', methods=['POST'])
def v4_website_screenshot():
    """التقاط صورة للموقع للنظام v4 مع حفظ تلقائي في المجلد"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        url = data.get('url')
        screenshot_id = data.get('screenshot_id', f'v4_screenshot_{int(time.time())}')
        report_id = data.get('report_id', f'report_{int(time.time())}')

        if not url:
            return jsonify({
                'success': False,
                'error': 'لا يوجد URL'
            }), 400

        logger.info(f"🎯 التقاط صورة للنظام v4: {url} -> {screenshot_id}")

        # التقاط الصورة باستخدام ScreenshotService
        result = screenshot_service.capture_screenshot(url, report_id, width=1920, height=1080, wait_time=3)

        if not result['success']:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        screenshot_data = result.get('screenshot_data') or result.get('base64_data')

        if not screenshot_data:
            return jsonify({
                'success': False,
                'error': 'فشل في التقاط الصورة'
            }), 500

        # حفظ الصورة في مجلد التقرير تلقائياً
        report_dir = Path("screenshots") / report_id
        report_dir.mkdir(parents=True, exist_ok=True)

        # تنظيف اسم الملف
        safe_filename = "".join(c for c in screenshot_id if c.isalnum() or c in (' ', '-', '_')).rstrip()
        if not safe_filename:
            safe_filename = f'v4_screenshot_{int(time.time())}'

        file_path = report_dir / f'{safe_filename}.png'

        # فك تشفير وحفظ
        image_data = base64.b64decode(screenshot_data)
        with open(file_path, 'wb') as f:
            f.write(image_data)

        logger.info(f"✅ تم التقاط وحفظ صورة v4: {file_path}")

        return jsonify({
            'success': True,
            'screenshot_data': screenshot_data,
            'file_path': str(file_path),
            'filename': f'{safe_filename}.png',
            'report_id': report_id,
            'screenshot_id': screenshot_id,
            'timestamp': time.time(),
            'method': 'selenium',
            'width': 1920,
            'height': 1080
        })

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صورة v4: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """الصفحة الرئيسية"""
    return jsonify({
        "service": "Python Screenshot Service v4.0",
        "status": "running",
        "endpoints": {
            "/health": "فحص صحة الخدمة",
            "/capture": "التقاط صورة (POST)",
            "/save_screenshot": "حفظ الصورة في مجلد (POST)",
            "/v4_website": "التقاط صورة للنظام v4 (POST)",
            "/vulnerability_sequence": "التقاط تسلسل صور للثغرة (POST)",
            "/stats": "إحصائيات الخدمة"
        }
    })

@app.route('/vulnerability_sequence', methods=['POST'])
def vulnerability_sequence():
    """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد)"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        url = data.get('url')
        vulnerability_name = data.get('vulnerability_name')
        report_id = data.get('report_id')

        if not all([url, vulnerability_name, report_id]):
            return jsonify({
                'success': False,
                'error': 'بيانات مفقودة: url, vulnerability_name, report_id'
            }), 400

        logger.info(f"🎯 التقاط تسلسل صور للثغرة: {vulnerability_name} -> {url}")

        # التقاط الصور الثلاث
        results = {}
        stages = ['before', 'during', 'after']

        for stage in stages:
            try:
                logger.info(f"📸 التقاط صورة {stage} للثغرة: {vulnerability_name}")

                # التقاط الصورة
                result = screenshot_service.capture_screenshot(url, report_id, width=1920, height=1080, wait_time=3)

                if result['success']:
                    results[stage] = {
                        'success': True,
                        'screenshot_data': result.get('screenshot_data') or result.get('base64_data'),
                        'base64': result.get('screenshot_data') or result.get('base64_data'),
                        'file_path': result.get('file_path'),
                        'timestamp': result.get('timestamp'),
                        'width': result.get('width', 1920),
                        'height': result.get('height', 1080),
                        'method': 'selenium_chrome',
                        'stage': stage
                    }
                    logger.info(f"✅ تم التقاط صورة {stage} بنجاح")
                else:
                    logger.error(f"❌ فشل في التقاط صورة {stage}: {result.get('error')}")
                    results[stage] = {
                        'success': False,
                        'error': result.get('error'),
                        'stage': stage
                    }

            except Exception as stage_error:
                logger.error(f"❌ خطأ في التقاط صورة {stage}: {stage_error}")
                results[stage] = {
                    'success': False,
                    'error': str(stage_error),
                    'stage': stage
                }

        # التحقق من نجاح التقاط الصور
        successful_stages = [stage for stage, result in results.items() if result.get('success')]

        logger.info(f"📊 نتائج التقاط تسلسل الصور: {len(successful_stages)}/3 نجحت")

        return jsonify({
            'success': len(successful_stages) > 0,
            'before': results.get('before'),
            'during': results.get('during'),
            'after': results.get('after'),
            'successful_stages': successful_stages,
            'total_stages': len(stages),
            'vulnerability_name': vulnerability_name,
            'report_id': report_id,
            'url': url,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط تسلسل صور الثغرة: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("🐍 بدء تشغيل خدمة Python لالتقاط الصور...")
    print("📡 الخدمة ستعمل على: http://localhost:8000")
    print("🔗 للاختبار: http://localhost:8000/health")
    
    try:
        app.run(host='0.0.0.0', port=8000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخدمة")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخدمة: {e}")
