<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدوال الشاملة الموجودة في v4</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .vulnerability-details {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار الدوال الشاملة الموجودة في v4</h1>
        <p>هذا الاختبار يتحقق من أن الدوال الشاملة الموجودة في النظام v4 تعمل بشكل صحيح وتعرض التفاصيل الحقيقية بدلاً من النص العام المكرر.</p>

        <div class="test-section">
            <h3>🔧 1. اختبار generateDetailedVulnerabilityChangesForReport</h3>
            <button onclick="testDetailedVulnerabilityChanges()">اختبار التغيرات التفصيلية</button>
            <div id="detailed-changes-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🔄 2. اختبار generatePageChanges</h3>
            <button onclick="testPageChanges()">اختبار تغيرات الصفحة</button>
            <div id="page-changes-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🎯 3. اختبار generateComprehensiveDetailsFromRealData</h3>
            <button onclick="testComprehensiveDetails()">اختبار التفاصيل الشاملة</button>
            <div id="comprehensive-details-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>📊 4. اختبار عرض التقرير النهائي</h3>
            <button onclick="testFinalReport()">اختبار التقرير النهائي</button>
            <div id="final-report-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>

        <div class="test-section">
            <h3>🔍 5. اختبار cleanDescription</h3>
            <button onclick="testCleanDescription()">اختبار تنظيف الوصف</button>
            <div id="clean-description-result" class="result info">انقر على الزر لبدء الاختبار...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                console.log('🔧 تهيئة نظام Bug Bounty v4...');
                bugBountyCore = new BugBountyCore();
                await bugBountyCore.initialize();
                console.log('✅ تم تهيئة النظام بنجاح');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                return false;
            }
        }

        // اختبار generateDetailedVulnerabilityChangesForReport
        async function testDetailedVulnerabilityChanges() {
            const resultDiv = document.getElementById('detailed-changes-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'SQL Injection',
                    severity: 'High',
                    location: 'https://example.com/login',
                    exploitation_result: {
                        response_analysis: 'تم رصد تغيير في استجابة الخادم: خطأ SQL',
                        response_code: 500,
                        response_headers: {'Content-Type': 'text/html'},
                        response_body_snippet: 'MySQL Error: You have an error in your SQL syntax'
                    },
                    visual_proof: {
                        response_code: 500,
                        response_headers: {'Server': 'Apache/2.4.41'},
                        response_body_snippet: 'Database error occurred'
                    },
                    testing_results: [
                        {
                            evidence: 'تم اكتشاف ثغرة SQL Injection',
                            response_snippet: 'Error in SQL syntax near \'\' OR \'1\'=\'1\''
                        }
                    ]
                };

                console.log('🔧 اختبار generateDetailedVulnerabilityChangesForReport...');
                const result = bugBountyCore.generateDetailedVulnerabilityChangesForReport(testVuln);
                
                resultDiv.innerHTML = `✅ نتيجة الاختبار:
                
<div class="vulnerability-details">
${result}
</div>

📊 معلومات إضافية:
- طول النتيجة: ${result.length} حرف
- يحتوي على HTML: ${result.includes('<') ? 'نعم' : 'لا'}
- يحتوي على تفاصيل حقيقية: ${result.includes('تم رصد تغيير') ? 'نعم' : 'لا'}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار generatePageChanges
        async function testPageChanges() {
            const resultDiv = document.getElementById('page-changes-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'XSS',
                    severity: 'Medium',
                    location: 'https://example.com/search',
                    page_changes: ['تم حقن كود JavaScript', 'ظهور نافذة تحذير', 'تغيير في DOM']
                };

                console.log('🔧 اختبار generatePageChanges...');
                const result = bugBountyCore.generatePageChanges(testVuln);
                
                resultDiv.textContent = `✅ نتيجة الاختبار:
                
النتيجة: ${JSON.stringify(result, null, 2)}
نوع النتيجة: ${typeof result}
هل هي مصفوفة: ${Array.isArray(result) ? 'نعم' : 'لا'}
عدد العناصر: ${Array.isArray(result) ? result.length : 'غير محدد'}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار generateComprehensiveDetailsFromRealData
        async function testComprehensiveDetails() {
            const resultDiv = document.getElementById('comprehensive-details-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testVuln = {
                    name: 'Command Injection',
                    severity: 'Critical',
                    location: 'https://example.com/exec',
                    exploitation_result: {
                        response_analysis: 'تم تنفيذ أمر النظام بنجاح',
                        response_code: 200,
                        response_body_snippet: 'uid=33(www-data) gid=33(www-data) groups=33(www-data)'
                    }
                };
                
                const realData = {
                    injection_point: '/exec?cmd=',
                    payload: '; whoami',
                    exploitation_result: 'تم تنفيذ الأمر وإرجاع معلومات المستخدم'
                };

                console.log('🔧 اختبار generateComprehensiveDetailsFromRealData...');
                const result = bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                resultDiv.innerHTML = `✅ نتيجة الاختبار:
                
<div class="vulnerability-details">
${result}
</div>

📊 معلومات إضافية:
- طول النتيجة: ${result.length} حرف
- يحتوي على HTML: ${result.includes('<') ? 'نعم' : 'لا'}
- يحتوي على البيانات الحقيقية: ${result.includes('injection_point') || result.includes('payload') ? 'نعم' : 'لا'}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التقرير النهائي
        async function testFinalReport() {
            const resultDiv = document.getElementById('final-report-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // محاكاة ثغرة مع تفاصيل شاملة
                const testVuln = {
                    name: 'SQL Injection',
                    severity: 'High',
                    location: 'https://example.com/login',
                    comprehensive_details: '<div>تفاصيل شاملة حقيقية للثغرة</div>',
                    description: 'SQL Injection NoSQL Injection Template Injection XPath Injection' // النص المكرر
                };

                // اختبار cleanDescription
                const cleanedDesc = bugBountyCore.cleanDescription(testVuln.description);
                
                resultDiv.textContent = `✅ نتيجة الاختبار:

الوصف الأصلي (المكرر):
"${testVuln.description}"

الوصف بعد التنظيف:
"${cleanedDesc}"

التفاصيل الشاملة:
${testVuln.comprehensive_details}

✅ تم تنظيف النص المكرر بنجاح!`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار cleanDescription
        async function testCleanDescription() {
            const resultDiv = document.getElementById('clean-description-result');
            resultDiv.textContent = 'جاري الاختبار...';
            resultDiv.className = 'result info';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                const testCases = [
                    'SQL Injection NoSQL Injection Template Injection XPath Injection',
                    'SQL Injection SQL Injection SQL Injection',
                    'XSS XSS XSS',
                    'Command Injection Command Injection',
                    'SQL Injection NoSQL Command Code LDAP XPath Template Injection'
                ];

                let results = '✅ نتائج اختبار تنظيف الوصف:\n\n';
                
                testCases.forEach((testCase, index) => {
                    const cleaned = bugBountyCore.cleanDescription(testCase);
                    results += `${index + 1}. الأصلي: "${testCase}"\n`;
                    results += `   المنظف: "${cleaned}"\n\n`;
                });

                resultDiv.textContent = results;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاختبار: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
